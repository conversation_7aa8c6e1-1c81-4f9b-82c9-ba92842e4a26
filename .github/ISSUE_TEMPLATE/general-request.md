---
name: General Request
about: A template for a general request on this repository
title: ''
labels: documentation, enhancement, question
assignees: ''

---

**:thought_balloon:	Description of the request or enhancement**
A clear and concise description of what the request is about. Please add the fitting label to this issue:
- Documentation
- Enhancement
- Question

**:bookmark: Additional context**
Add any other context or screenshots about the feature request here.

**:100: Acceptance criteria**
Enter the conditions of satisfaction here. That is, the conditions that will satisfy the user/persona that the goal/benefit/value has been achieved.
