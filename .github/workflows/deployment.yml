# This is a basic workflow to help you get started with Actions

name: Continuous Deployment Pipeline

# Controls when the action will run. Triggers the workflow on push or pull request
# events but for the relative branch
on:
  push:
    branches:
      - development
      - PreProd
      - Prod
  pull_request:
    branches:
      - development
      - PreProd
      - Prod

# A workflow run is made up of one or more jobs that can run sequentially or in parallel
jobs:
  Build:
    # The type of runner that the job will run on
    runs-on: ubuntu-latest
    # Steps represent a sequence of tasks that will be executed as part of the job
    steps:
      # Checks-out your repository under $GITHUB_WORKSPACE, so your job can access it
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0 # Fetch full history to access source branch commits

      - name: 📊 Deployment Analysis
        run: |
          printf '\033[1;34m🔍 DEPLOYMENT ANALYSIS\033[0m\n'
          printf '\033[1;33m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\033[0m\n'
          echo ""

          # Environment detection
          BRANCH_NAME=${GITHUB_REF#refs/heads/}
          case $BRANCH_NAME in
            "development") ENV="🟢 Development" ;;
            "qa") ENV="🟡 QA/Test" ;;
            "PreProd") ENV="🟠 Pre-Production" ;;
            "Prod") ENV="🔴 Production" ;;
            *) ENV="🔵 Development" ;;
          esac

          printf '\033[1;36m📍 Target Environment:\033[0m %s\n' "$ENV"
          printf '\033[1;36m🌿 Branch:\033[0m %s\n' "$BRANCH_NAME"
          printf '\033[1;36m📝 Commit:\033[0m %s\n' "${GITHUB_SHA:0:8}"
          printf '\033[1;36m👤 Triggered by:\033[0m %s\n' "$GITHUB_ACTOR"
          printf '\033[1;36m🕐 Triggered at:\033[0m %s\n' "$(TZ='Europe/Belgrade' date '+%Y-%m-%d %H:%M:%S %Z')"
          printf '\033[1;36m🎯 Event:\033[0m %s\n' "$GITHUB_EVENT_NAME"
          echo ""
          echo ""

          # Triggering commit details
          printf '\033[1;35m📝 Triggering Commit Details:\033[0m\n'

          # Determine the correct commit and fetch details based on event type
          if [ "$GITHUB_EVENT_NAME" = "pull_request" ]; then
            # For pull requests, get details from the source branch
            if [ -n "$GITHUB_HEAD_REF" ]; then
              printf '\033[1;36m   • Event Type:\033[0m Pull Request\n'
              printf '\033[1;36m   • Source Branch:\033[0m %s\n' "$GITHUB_HEAD_REF"
              printf '\033[1;36m   • Target Branch:\033[0m %s\n' "$GITHUB_BASE_REF"
              
              # For pull requests, try to get the actual source branch commit (not the merge commit)
              # First try HEAD^2 which should be the feature branch in a merge commit
              COMMIT_MESSAGE=$(git log -1 --format="%s" HEAD^2 2>/dev/null || git log -1 --format="%s" --no-merges HEAD 2>/dev/null || git log -1 --format="%s" "origin/$GITHUB_HEAD_REF" 2>/dev/null || echo "Unable to fetch commit message")
              COMMIT_AUTHOR=$(git log -1 --format="%an" HEAD^2 2>/dev/null || git log -1 --format="%an" --no-merges HEAD 2>/dev/null || git log -1 --format="%an" "origin/$GITHUB_HEAD_REF" 2>/dev/null || echo "Unknown")
              COMMIT_DATE=$(git log -1 --format="%ci" HEAD^2 2>/dev/null || git log -1 --format="%ci" --no-merges HEAD 2>/dev/null || git log -1 --format="%ci" "origin/$GITHUB_HEAD_REF" 2>/dev/null || echo "Unknown")
            else
              printf '\033[1;36m   • Event Type:\033[0m Pull Request (fallback to merge commit)\n'
              COMMIT_MESSAGE=$(git log -1 --format="%s" --no-merges HEAD 2>/dev/null || git log -1 --format="%s" HEAD 2>/dev/null || echo "Unable to fetch commit message")
              COMMIT_AUTHOR=$(git log -1 --format="%an" --no-merges HEAD 2>/dev/null || git log -1 --format="%an" HEAD 2>/dev/null || echo "Unknown")
              COMMIT_DATE=$(git log -1 --format="%ci" --no-merges HEAD 2>/dev/null || git log -1 --format="%ci" HEAD 2>/dev/null || echo "Unknown")
            fi
          else
            # For push events, use the actual commit
            printf '\033[1;36m   • Event Type:\033[0m Push\n'
            COMMIT_MESSAGE=$(git log -1 --format="%s" "$GITHUB_SHA" 2>/dev/null || echo "Unable to fetch commit message")
            COMMIT_AUTHOR=$(git log -1 --format="%an" "$GITHUB_SHA" 2>/dev/null || echo "Unknown")
            COMMIT_DATE=$(git log -1 --format="%ci" "$GITHUB_SHA" 2>/dev/null || echo "Unknown")
          fi

          printf '\033[1;36m   • Message:\033[0m %s\n' "$COMMIT_MESSAGE"
          printf '\033[1;36m   • Author:\033[0m %s\n' "$COMMIT_AUTHOR"
          printf '\033[1;36m   • Date:\033[0m %s\n' "$COMMIT_DATE"
          echo ""
          echo ""

          # Changed files analysis
          printf '\033[1;32m📁 Changed Files in This Deployment:\033[0m\n'
          if git diff --name-status HEAD~1 HEAD 2>/dev/null | head -20; then
            TOTAL_CHANGES=$(git diff --name-status HEAD~1 HEAD 2>/dev/null | wc -l)
            printf '\033[1;33m📊 Total files changed: %s\033[0m\n' "$TOTAL_CHANGES"
            echo ""
            
            # Highlight critical files
            printf '\033[1;31m🚨 Critical Configuration Files Changed:\033[0m\n'
            git diff --name-status HEAD~1 HEAD 2>/dev/null | grep -E '\.(xml|yaml|yml|properties)$' || echo "   ✅ No critical configuration files changed"
            echo ""
            
            # Show MuleSoft specific changes
            printf '\033[1;34m🔧 MuleSoft Application Files Changed:\033[0m\n'
            git diff --name-status HEAD~1 HEAD 2>/dev/null | grep -E 'src/main/mule/|src/main/resources/' || echo "   ✅ No MuleSoft application files changed"
            echo ""
            
            # Show test changes
            printf '\033[1;36m🧪 Test Files Changed:\033[0m\n'
            git diff --name-status HEAD~1 HEAD 2>/dev/null | grep -E 'src/test/' || echo "   ✅ No test files changed"
          else
            echo "   ℹ️  Unable to detect changes (possibly first commit or shallow clone)"
          fi
          echo ""
          printf '\033[1;33m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\033[0m\n'

      # Use cache for dependencies
      - uses: actions/cache@v4
        with:
          path: ~/.m2/repository
          key: ${{ runner.os }}-maven-${{ hashFiles('**/pom.xml') }}
          restore-keys: |
            ${{ runner.os }}-maven-
      # Set up java version
      - name: Set up JDK 17
        uses: actions/setup-java@v4
        with:
          distribution: "temurin"
          java-version: 17
      # Enhanced Maven build with progress indicators
      - name: 🔨 Build MuleSoft Application
        run: |
          printf '\033[1;33m🟡 Starting Maven build process...\033[0m\n'
          printf '\033[1;36m📦 Building prc-partners MuleSoft application\033[0m\n'
          echo ""

          BRANCH_NAME=${GITHUB_REF#refs/heads/}
          if [ "$BRANCH_NAME" != "Prod" ]; then
            printf '\033[1;34m🔧 Non-Production Build Configuration\033[0m\n'
            printf '\033[1;36m   • Environment: Development/QA\033[0m\n'
            printf '\033[1;36m   • Skip MUnit Tests: Yes\033[0m\n'
            printf '\033[1;36m   • Using Non-Prod Credentials\033[0m\n'
            echo ""
            mvn clean package -Dbuild_id=${GITHUB_SHA:0:8}  -s settings.xml
          else
            printf '\033[1;31m🏭 Production Build Configuration\033[0m\n'
            printf '\033[1;36m   • Environment: Production\033[0m\n'
            printf '\033[1;36m   • Skip MUnit Tests: Yes\033[0m\n'
            printf '\033[1;36m   • Using Production Credentials\033[0m\n'
            echo ""
            mvn clean package -Dbuild_id=${GITHUB_SHA:0:8}  -s settings.xml
          fi
          echo ""

          printf '\033[1;32m✅ Build completed successfully!\033[0m\n'
          printf '\033[1;36m📋 Build artifacts ready for deployment\033[0m\n'
        env:
          REPOSITORY_USER: ${{ secrets.becse_int_repository_user }}
          REPOSITORY_PASS: ${{ secrets.becse_int_repository_pass }}
          EXCHANGE_CLIENT: ${{ github.ref == 'refs/heads/Prod' && secrets.becse_connected_app_prod_client_id || secrets.becse_connected_app_non_prod_client_id }}
          EXCHANGE_SECRET: ${{ github.ref == 'refs/heads/Prod' && secrets.becse_connected_app_prod_client_secret || secrets.becse_connected_app_non_prod_client_secret }}

  PublishToExchange:
    # The type of runner that the job will run on
    runs-on: ubuntu-latest
    needs: [Build]
    # Steps represent a sequence of tasks that will be executed as part of the job
    steps:
      # Checks-out your repository under $GITHUB_WORKSPACE, so your job can access it
      - uses: actions/checkout@v4
        with:
          fetch-depth: 2
      # Exchange Publication Analysis
      - name: 📤 Exchange Publication Analysis
        run: |
          printf '\033[1;34m📤 ANYPOINT EXCHANGE PUBLICATION\033[0m\n'
          printf '\033[1;33m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\033[0m\n'
          printf '\033[1;36m🎯 Target:\033[0m Anypoint Exchange Repository\n'
          printf '\033[1;36m🌿 Branch:\033[0m %s\n' "${GITHUB_REF#refs/heads/}"
          echo ""
          printf '\033[1;35m📋 API Specification Changes:\033[0m\n'
          git diff --name-status HEAD~1 HEAD 2>/dev/null | grep -E '\.raml$|\.json$|exchange-docs/' || echo "   ✅ No API specification changes detected"
          echo ""
          printf '\033[1;35m📚 Documentation Changes:\033[0m\n'
          git diff --name-status HEAD~1 HEAD 2>/dev/null | grep -E 'src/main/doc/|exchange-docs/' || echo "   ✅ No documentation changes detected"
          printf '\033[1;33m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\033[0m\n'

      # Test - use cache for dependencies
      - uses: actions/cache@v4
        with:
          path: ~/.m2/repository
          key: ${{ runner.os }}-maven-${{ hashFiles('**/pom.xml') }}
          restore-keys: |
            ${{ runner.os }}-maven-
      # Check the XML Settings used and publish to Mulesoft Exchange Repository
      - name: Publish to Exchange
        if: ${{ (github.ref == 'refs/heads/development') || (github.ref == 'refs/heads/development-azure') }}
        run: |
          printf '\033[1;33m🟡 Publishing to Anypoint Exchange...\033[0m\n'
          printf '\033[1;36m📋 Publishing API specification and documentation\033[0m\n'
          printf '\033[1;36m🔗 Making API discoverable in Exchange\033[0m\n'
          echo ""

          mvn deploy -Dbuild_id=${GITHUB_SHA:0:8}  -s settings.xml
          echo ""

          printf '\033[1;32m✅ Successfully published to Anypoint Exchange!\033[0m\n'
          printf '\033[1;36m🌐 API now available in Exchange for discovery\033[0m\n'
        env:
          REPOSITORY_USER: ${{ secrets.becse_int_repository_user }}
          REPOSITORY_PASS: ${{ secrets.becse_int_repository_pass }}
          EXCHANGE_CLIENT: ${{ secrets.becse_connected_app_non_prod_client_id }}
          EXCHANGE_SECRET: ${{ secrets.becse_connected_app_non_prod_client_secret }}
  Deployment:
    # The type of runner that the job will run on
    runs-on: ubuntu-latest
    if: success()
    needs: [Build, PublishToExchange]

    # Steps represent a sequence of tasks that will be executed as part of the job
    steps:
      # Checks-out your repository under $GITHUB_WORKSPACE, so your job can access it
      - uses: actions/checkout@v4
        with:
          fetch-depth: 2
        # Deployment Configuration Analysis
      - name: 🚀 Deployment Configuration Analysis
        run: |
          BRANCH_NAME=${GITHUB_REF#refs/heads/}

          printf '\033[1;34m🚀 MULESOFT RTF DEPLOYMENT\033[0m\n'
          printf '\033[1;33m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\033[0m\n'

          case $BRANCH_NAME in
            "development")
              printf '\033[1;32m🎯 Environment:\033[0m Development RTF\n'
              printf '\033[1;32m🏗️  Fabric:\033[0m becse-cf2-dev-fabric\n'
              printf '\033[1;32m🌐 FQDN:\033[0m rtf-mule.shap.becse.dev.eu-int-aholddelhaize.com\n'
              printf '\033[1;32m⚙️  Resources:\033[0m CPU: 80m-100m, Memory: 1000Mi\n'
              printf '\033[1;32m🔄 Strategy:\033[0m Rolling Update\n'
              ;;
            "qa")
              printf '\033[1;33m🎯 Environment:\033[0m QA/Test RTF\n'
              printf '\033[1;33m🏗️  Fabric:\033[0m becse-cf2-dev-fabric\n'
              printf '\033[1;33m🌐 FQDN:\033[0m rtf-mule.shap.becse.dev.eu-int-aholddelhaize.com\n'
              printf '\033[1;33m⚙️  Resources:\033[0m CPU: 80m-100m, Memory: 1000Mi\n'
              printf '\033[1;33m🔄 Strategy:\033[0m Rolling Update\n'
              ;;
            "PreProd")
              printf '\033[1;35m🎯 Environment:\033[0m Pre-Production RTF\n'
              printf '\033[1;35m🏗️  Fabric:\033[0m becse-cf2-acc-fabric\n'
              printf '\033[1;35m🌐 FQDN:\033[0m rtf-mule.shap.becse.acc.eu-int-aholddelhaize.com\n'
              printf '\033[1;35m⚙️  Resources:\033[0m CPU: 50m-100m, Memory: 1000Mi\n'
              printf '\033[1;35m🔄 Strategy:\033[0m Rolling Update\n'
              printf '\033[1;35m🔐 Vault:\033[0m qa-muleapps-keyvault\n'
              ;;
            "Prod")
              printf '\033[1;31m🎯 Environment:\033[0m Production RTF\n'
              printf '\033[1;31m🏗️  Fabric:\033[0m becse-cf2-prod-fabric\n'
              printf '\033[1;31m🌐 FQDN:\033[0m rtf-mule.shap.becse.prd.eu-int-aholddelhaize.com\n'
              printf '\033[1;31m⚙️  Resources:\033[0m CPU: 80m-100m, Memory: 1000Mi\n'
              printf '\033[1;31m🔄 Strategy:\033[0m Rolling Update\n'
              printf '\033[1;31m🔐 Vault:\033[0m prd-muleapps-keyvault\n'
              ;;
          esac

          printf '\033[1;36m📊 Replicas:\033[0m 1\n'
          printf '\033[1;36m🔧 Cluster Mode:\033[0m Disabled\n'
          printf '\033[1;36m📱 Application:\033[0m prc-partners\n'
          printf '\033[1;33m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\033[0m\n'
      # Use cache for dependencies
      - uses: actions/cache@v4
        with:
          path: ~/.m2/repository
          key: ${{ runner.os }}-maven-${{ hashFiles('**/pom.xml') }}
          restore-keys: |
            ${{ runner.os }}-maven-

      - name: Deploy to AKS RTF Development
        if: github.ref == 'refs/heads/development'
        run: |
          printf '\033[1;33m🟡 Deploying to Development RTF Environment...\033[0m\n'
          printf '\033[1;36m📦 Deploying prc-partners to becse-cf2-dev-fabric\033[0m\n'
          printf '\033[1;36m🔧 Configuration: Development settings\033[0m\n'
          echo ""

          mvn deploy -DmuleDeploy -Dbuild_id=${GITHUB_SHA:0:8} -DskipMunitTests -Drewrite="(/|$)(.*)" -Drtf-name=becse-cf2-dev-fabric -Denv_replicas=1 -Dmule_env=Development -Dfqdn_env=rtf-mule.shap.becse.dev.eu-int-aholddelhaize.com -Dparam_env=dev -Dmule_app_cpu=150m -Dmule_app_cpu_max=200m -Dmule_app_mem=2000Mi -Dclient_id=${{ secrets.becse_anypoint_client_id_dev}} -Dclient_secret=${{ secrets.becse_anypoint_client_secret_dev}} -Dvault_client_id=${{ secrets.becse_keyvault_client_id_mule_dev}} -Dvault_client_secret=${{ secrets.becse_keyvault_client_secret_mule_dev}} -Dvault_encrypt_key=${{ secrets.becse_keyvault_encrypt_key_mule_dev}} -Dvault_name=kvbecsmuledwe01 -Dparam_app_client=${{ secrets.becse_connected_app_non_prod_client_id}} -Dparam_app_secret=${{ secrets.becse_connected_app_non_prod_client_secret}} -Denforce_replicas_accross_nodes=false -Dcluster_mode=false -Dupdate_strategy=recreate -Djson_logger_pretty_print=false -s settings.xml -Dsvc_postfix_uri=90bd1d54-139e-4fc1-b6a6-d4e14b33ba3d.svc
          echo ""


          printf '\033[1;32m✅ Development deployment completed successfully!\033[0m\n'
          printf '\033[1;36m🌐 Application URL:\033[0m https://rtf-mule.shap.becse.dev.eu-int-aholddelhaize.com/v1/\n'
          printf '\033[1;36m📋 Health Check:\033[0m https://rtf-mule.shap.becse.dev.eu-int-aholddelhaize.com/v1/healthcheck\n'

      - name: Deploy to AKS RTF PreProd
        if: github.ref == 'refs/heads/PreProd'
        run: |
          printf '\033[1;35m🟡 Deploying to Pre-Production RTF Environment...\033[0m\n'
          printf '\033[1;36m📦 Deploying prc-partners to becse-cf2-acc-fabric\033[0m\n'
          printf '\033[1;36m🔧 Configuration: Pre-Production settings with KeyVault\033[0m\n'
          echo ""
          mvn deploy -DmuleDeploy -Dbuild_id=${GITHUB_SHA:0:8} -DskipMunitTests -Drewrite="(/|$)(.*)" -Drtf-name=becse-cf2-acc-fabric -Denv_replicas=1 -Dmule_env=Acceptance -Dfqdn_env=rtf-mule.shap.becse.acc.eu-int-aholddelhaize.com -Dparam_env=qa -Dmule_app_cpu=120m -Dmule_app_cpu_max=220m -Dmule_app_mem=1500Mi -Dclient_id=${{ secrets.becse_anypoint_client_id_pre}} -Dclient_secret=${{ secrets.becse_anypoint_client_secret_pre}} -Dvault_client_id=${{ secrets.becse_keyvault_client_id_mule_acc}} -Dvault_client_secret=${{ secrets.becse_keyvault_client_secret_mule_acc}} -Dvault_encrypt_key=${{ secrets.becse_keyvault_encrypt_key_mule_acc}} -Dvault_name=kvbecsmuleawe01 -Dparam_app_client=${{ secrets.becse_connected_app_non_prod_client_id}} -Dparam_app_secret=${{ secrets.becse_connected_app_non_prod_client_secret}} -Denforce_replicas_accross_nodes=true -Dcluster_mode=true -Dupdate_strategy=rolling -Djson_logger_pretty_print=false -s settings.xml -Dsvc_postfix_uri=fb045a77-8155-4e02-83f0-b82afaff26a4.svc
          echo ""        
          printf '\033[1;32m✅ Pre-Production deployment completed successfully!\033[0m\n'
          printf '\033[1;36m🌐 Application URL:\033[0m https://rtf-mule.shap.becse.acc.eu-int-aholddelhaize.com/v1/\n'
          printf '\033[1;36m📋 Health Check:\033[0m https://rtf-mule.shap.becse.acc.eu-int-aholddelhaize.com/v1/healthcheck\n'

      - name: Deploy to AKS RTF Prod
        if: github.ref == 'refs/heads/Prod'
        run: |
          printf '\033[1;31m🟡 Deploying to Production RTF Environment...\033[0m\n'
          printf '\033[1;36m📦 Deploying prc-partners to becse-cf2-prod-fabric\033[0m\n'
          printf '\033[1;36m🔧 Configuration: Production settings with KeyVault\033[0m\n'
          printf '\033[1;31m⚠️  PRODUCTION DEPLOYMENT - Extra care required\033[0m\n'

          mvn deploy -DmuleDeploy -Dbuild_id=${GITHUB_SHA:0:8} -DskipMunitTests -Drewrite="(/|$)(.*)" -Drtf-name=becse-cf2-prod-fabric -Denv_replicas=1 -Dmule_env=Production -Dfqdn_env=rtf-mule.shap.becse.prd.eu-int-aholddelhaize.com -Dparam_env=prd -Dmule_app_cpu=180m -Dmule_app_cpu_max=300m -Dmule_app_mem=2000Mi -Dclient_id=${{ secrets.becse_anypoint_client_id_prod}} -Dclient_secret=${{ secrets.becse_anypoint_client_secret_prod}} -Dvault_client_id=${{ secrets.becse_keyvault_client_id_mule_prd}} -Dvault_client_secret=${{ secrets.becse_keyvault_client_secret_mule_prd}} -Dvault_encrypt_key=${{ secrets.becse_keyvault_encrypt_key_mule_prd}} -Dvault_name=kvbecsmulepwe01  -Dparam_app_client=${{ secrets.becse_connected_app_prod_client_id }} -Dparam_app_secret=${{ secrets.becse_connected_app_prod_client_secret }} -Denforce_replicas_accross_nodes=true -Dcluster_mode=true -Dupdate_strategy=rolling -Djson_logger_pretty_print=false -s settings.xml -Dsvc_postfix_uri=b9b4cbfe-0687-4fd2-869f-e6067ca15a14.svc

          printf '\033[1;32m✅ Production deployment completed successfully!\033[0m\n'
          printf '\033[1;36m🌐 Application URL:\033[0m https://rtf-mule.shap.becse.prd.eu-int-aholddelhaize.com/v1/\n'
          printf '\033[1;36m📋 Health Check:\033[0m https://rtf-mule.shap.becse.prd.eu-int-aholddelhaize.com/v1/healthcheck\n'
        env:
          REPOSITORY_USER: ${{ secrets.becse_int_repository_user }}
          REPOSITORY_PASS: ${{ secrets.becse_int_repository_pass }}
          EXCHANGE_CLIENT: ${{ secrets.becse_connected_app_prod_client_id }}
          EXCHANGE_SECRET: ${{ secrets.becse_connected_app_prod_client_secret }}
      # Deployment Success Summary
      - name: 📋 Deployment Success Summary
        if: success()
        run: |
          BRANCH_NAME=${GITHUB_REF#refs/heads/}

          printf '\033[1;32m🎉 DEPLOYMENT PIPELINE COMPLETED SUCCESSFULLY\033[0m\n'
          printf '\033[1;33m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\033[0m\n'
          printf '\033[1;36m✅ Build Phase:\033[0m Completed Successfully\n'
          printf '\033[1;36m✅ Exchange Publication:\033[0m API Published to Exchange\n'
          printf '\033[1;36m✅ RTF Deployment:\033[0m Application Deployed Successfully\n'
          printf '\033[1;36m🎯 Target Environment:\033[0m %s\n' "$BRANCH_NAME"
          printf '\033[1;36m📱 Application:\033[0m prc-partners\n'
          printf '\033[1;36m🕐 Completed at:\033[0m %s\n' "$(TZ='Europe/Belgrade' date '+%Y-%m-%d %H:%M:%S %Z')"
          printf '\033[1;36m🔗 Commit:\033[0m %s\n' "${GITHUB_SHA:0:8}"
          printf '\033[1;36m👤 Deployed by:\033[0m %s\n' "$GITHUB_ACTOR"
          echo ""
          printf '\033[1;35m🔗 Quick Links:\033[0m\n'
          case $BRANCH_NAME in
            "development"|"qa")
              printf '\033[1;36m   • API Base URL:\033[0m https://rtf-mule.shap.becse.dev.eu-int-aholddelhaize.com/v1/\n'
              printf '\033[1;36m   • Health Check:\033[0m https://rtf-mule.shap.becse.dev.eu-int-aholddelhaize.com/v1/healthcheck\n'
              printf '\033[1;36m   • API Console:\033[0m https://rtf-mule.shap.becse.dev.eu-int-aholddelhaize.com/v1/console/\n'
              ;;
            "PreProd")
              printf '\033[1;36m   • API Base URL:\033[0m https://rtf-mule.shap.becse.acc.eu-int-aholddelhaize.com/v1/\n'
              printf '\033[1;36m   • Health Check:\033[0m https://rtf-mule.shap.becse.acc.eu-int-aholddelhaize.com/v1/healthcheck\n'
              printf '\033[1;36m   • API Console:\033[0m https://rtf-mule.shap.becse.acc.eu-int-aholddelhaize.com/v1/console/\n'
              ;;
            "Prod")
              printf '\033[1;36m   • API Base URL:\033[0m https://rtf-mule.shap.becse.prd.eu-int-aholddelhaize.com/v1/\n'
              printf '\033[1;36m   • Health Check:\033[0m https://rtf-mule.shap.becse.prd.eu-int-aholddelhaize.com/v1/healthcheck\n'
              printf '\033[1;36m   • API Console:\033[0m https://rtf-mule.shap.becse.prd.eu-int-aholddelhaize.com/v1/console/\n'
              ;;
          esac
          printf '\033[1;33m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\033[0m\n'
          printf '\033[1;32m🚀 prc-partners is now live and ready to serve requests!\033[0m\n'

      # Deployment Failure Summary
      - name: 📋 Deployment Failure Summary
        if: failure()
        run: |
          printf '\033[1;31m❌ DEPLOYMENT PIPELINE FAILED\033[0m\n'
          printf '\033[1;33m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\033[0m\n'
          printf '\033[1;36m🎯 Target Environment:\033[0m %s\n' "${GITHUB_REF#refs/heads/}"
          printf '\033[1;36m📱 Application:\033[0m prc-partners\n'
          printf '\033[1;36m🕐 Failed at:\033[0m %s\n' "$(TZ='Europe/Belgrade' date '+%Y-%m-%d %H:%M:%S %Z')"
          printf '\033[1;36m🔗 Commit:\033[0m %s\n' "${GITHUB_SHA:0:8}"
          printf '\033[1;36m👤 Triggered by:\033[0m %s\n' "$GITHUB_ACTOR"
          echo ""
          printf '\033[1;31m🔍 Please check the logs above for detailed error information\033[0m\n'
          printf '\033[1;33m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\033[0m\n'
