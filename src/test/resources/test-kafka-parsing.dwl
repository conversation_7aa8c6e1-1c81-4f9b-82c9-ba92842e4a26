%dw 2.0
output application/json

// Test data based on the actual Kafka message structure from the logs
var testPayload = [
    {
        "serByteArray": {
            "inputStream": '{"domain": "delhaize","businessPartnerId": "40541451398_dll","name": "(AD Test) Generated Company Name 95fe51aa-0033-4511-8157-e3070ba4d469","eudrRelevanceIndicator": true,"businessPartnerTypeCode": 1,"businessRelationshipTypeCode": 1,"tagList": [{"tagKey": "DESADV"}],"address": {"city": "Mannheim","postalCode": "68163","street": "Julius-Hatry-Str.","houseNumber": "103A","countryCode": "DE"},"identifiers": {"vatID": "DE435933283397565574","glnId": "GLN_435933284","dunsId": "DUNS_435933285","cocRegisterId": "COC_REG_435933286","cocRegistrarName": "RegistrarName_435933287"},"contactPersonList": [{"contactId": 1,"personTitleCode": 1,"defaultContactIndicator": true,"firstName": "FirstName435933288","lastName": "LastName435933289","languageCode": "de","emailAddress": "<EMAIL>","mobilePhoneNumber": "+49-175-1234567","responsibilities": [{"defaultAssignments": "EUDR"}]}],"responsiblePurchaser": {"personTitleCode": 2,"firstName": "FirstName435933291","lastName": "LastName435933292","languageCode": "en","emailAddress": "<EMAIL>","mobilePhoneNumber": "+43-175-7410258"}}'
        },
        "payload": "",
        "attributes": {
            "key": '"40541451398_dll"'
        }
    }
]

// Functions from the fixed parseKafkaMessages.dwl
fun safeJsonParse(value, defaultOutput = null) =
    dw::Runtime::try(() ->
        if (value is String)
            read(value, "application/json")
        else
            value
    ) match {
        case tr if (tr.success) -> tr.result
        else -> defaultOutput
    }

fun isValidPayload(value) =
    value != null and
    value != "" and
    not isEmpty(value)

fun extractKafkaPayload(kafkaMessage) = do {
    // First try to get the payload from serByteArray.inputStream (new Kafka structure)
    // If that doesn't exist, fall back to the payload field (legacy structure)
    var messagePayload = 
        if (kafkaMessage.serByteArray.inputStream != null) 
            kafkaMessage.serByteArray.inputStream
        else 
            kafkaMessage.payload
    
    var cleanedPayload = if (messagePayload is String and messagePayload != null and messagePayload != "")
        trim(messagePayload)
    else
        messagePayload
    var parsedPayload = if (cleanedPayload is String and cleanedPayload != null and cleanedPayload != "")
        safeJsonParse(cleanedPayload)
    else if (cleanedPayload != null)
        cleanedPayload
    else
        null
    ---
    if (isValidPayload(parsedPayload)) parsedPayload else null
}

// Test the extraction
var extractedPayloads = testPayload map extractKafkaPayload($)
var validMessages = extractedPayloads filter ($ != null)

---
{
    "testResult": if (sizeOf(validMessages) > 0) "SUCCESS" else "FAILED",
    "extractedDataArray": validMessages,
    "arraySize": sizeOf(validMessages),
    "firstElementFields": if (sizeOf(validMessages) > 0) {
        "domain": validMessages[0].domain,
        "businessPartnerId": validMessages[0].businessPartnerId,
        "name": validMessages[0].name
    } else null
}
