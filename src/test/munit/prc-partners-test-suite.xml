<?xml version="1.0" encoding="UTF-8"?>
<mule xmlns="http://www.mulesoft.org/schema/mule/core" xmlns:doc="http://www.mulesoft.org/schema/mule/documentation" xmlns:http="http://www.mulesoft.org/schema/mule/http" xmlns:munit="http://www.mulesoft.org/schema/mule/munit" xmlns:munit-tools="http://www.mulesoft.org/schema/mule/munit-tools" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.mulesoft.org/schema/mule/core http://www.mulesoft.org/schema/mule/core/current/mule.xsd http://www.mulesoft.org/schema/mule/http http://www.mulesoft.org/schema/mule/http/current/mule-http.xsd http://www.mulesoft.org/schema/mule/munit http://www.mulesoft.org/schema/mule/munit/current/mule-munit.xsd http://www.mulesoft.org/schema/mule/munit-tools http://www.mulesoft.org/schema/mule/munit-tools/current/mule-munit-tools.xsd ">
    <!-- Connector Configurations Start -->
    <munit:config name="prc-partners-test-suite.xml" />
    <http:request-config name="HTTP_Request_Configuration" basePath="/v1">
        <http:request-connection host="${api.http.listener.host}" port="${api.http.listener.port}" />
    </http:request-config>
    <!-- Connector Configurations End -->
   
   <!-- Negative Test Cases Start -->
    <munit:test name="munit-global-bad-request-response-sub-flow" description="Verifying functionality of global-bad-request-response-sub-flow" doc:id="8ba81218-7d40-4a54-ab70-dfaf51d2c76a">
		<munit:enable-flow-sources >
			<munit:enable-flow-source value="prc-partners-api-main" />
			<munit:enable-flow-source value="get:\employee:prc-partners-config" />
		</munit:enable-flow-sources>
		<munit:execution>
			<http:request method="GET" doc:name="HTTP Request" doc:id="6b9a51b2-41c4-444f-973e-dbe5849e1d21" config-ref="HTTP_Request_Configuration" path="/employee">
				<http:headers><![CDATA[#[output application/java
---
{
	"Content-Type" : "application/json",
	"client-id" : "client-id"
}]]]></http:headers>
				<http:response-validator >
					<http:success-status-code-validator values="300..500" />
				</http:response-validator>
			</http:request>
		</munit:execution>
		<munit:validation >
			<munit-tools:assert-that doc:name="Assert That Status Code is 400" doc:id="153e2f37-0473-4c30-a14a-bd3d3cc800c8" expression="#[attributes.statusCode]" is="#[MunitTools::equalTo(400)]" message="The HTTP Status code is not correct!" />
			<munit-tools:assert-that doc:name="Assert That - Payload is Expected" doc:id="6f2fba47-e864-48f3-a582-46e16e3557e4" expression="#[payload.error.errorDescription]" is="#[MunitTools::equalTo(&quot;Required header 'client-secret' not specified&quot;)]" message="The response payload is not correct!" />
		</munit:validation>
	</munit:test>
	    <munit:test name="munit-global-resource-not-found-response-sub-flow" description="Verifying functionality of global-resource-not-found-response-sub-flow" doc:id="0b495ebd-f8cb-4eca-9734-784e1b324302">
		<munit:enable-flow-sources >
			<munit:enable-flow-source value="prc-partners-api-main" />
			<munit:enable-flow-source value="get:\employee:prc-partners-config" />
		</munit:enable-flow-sources>
		<munit:execution>
			<http:request method="GET" doc:name="HTTP Request" doc:id="6d9fca17-54f9-46bf-a350-859080c91975" config-ref="HTTP_Request_Configuration" path="/employees">
				<http:headers><![CDATA[#[output application/java
---
{
	"Content-Type" : "application/json",
	"client-id" : "client-id",
	"client-secret" : "client-secret"
}]]]></http:headers>
				<http:response-validator >
					<http:success-status-code-validator values="300..500" />
				</http:response-validator>
			</http:request>
		</munit:execution>
		<munit:validation >
			<munit-tools:assert-that doc:name="Assert That Status Code is 404" doc:id="ffde6ece-4d3e-4662-85fd-702bd54c42bc" expression="#[attributes.statusCode]" is="#[MunitTools::equalTo(404)]" message="The HTTP Status code is not correct!" />
			<munit-tools:assert-that doc:name="Assert That - Payload is Expected" doc:id="707b2062-1025-4b20-9add-8526134b5ddd" expression="#[payload.error.errorMessage]" is="#[MunitTools::equalTo('The server has not found anything matching the Request-URI')]" message="The response payload is not correct!" />
		</munit:validation>
	</munit:test>
		    <munit:test name="munit-global-method-not-allowed-response-sub-flow" description="Verifying functionality of global-method-not-allowed-response-sub-flow" doc:id="5011b7fc-b5da-4210-97c8-e3f7779b763f">
		<munit:enable-flow-sources >
			<munit:enable-flow-source value="prc-partners-api-main" />
			<munit:enable-flow-source value="get:\employee:prc-partners-config" />
		</munit:enable-flow-sources>
		<munit:execution>
			<http:request method="POST" doc:name="HTTP Request" doc:id="ea40d559-3de1-42c4-895e-34a39e0edf2c" config-ref="HTTP_Request_Configuration" path="/employee">
				<http:headers><![CDATA[#[output application/java
---
{
	"Content-Type" : "application/json",
	"client-id" : "client-id",
	"client-secret" : "client-secret"
}]]]></http:headers>
				<http:response-validator >
					<http:success-status-code-validator values="300..500" />
				</http:response-validator>
			</http:request>
		</munit:execution>
		<munit:validation >
			<munit-tools:assert-that doc:name="Assert That Status Code is 405" doc:id="434b3644-e74e-4fc0-b225-f4c982c89bb4" expression="#[attributes.statusCode]" is="#[MunitTools::equalTo(405)]" message="The HTTP Status code is not correct!" />
			<munit-tools:assert-that doc:name="Assert That - Payload is Expected" doc:id="4ca23de9-a2be-4e66-b549-c2e19b1506d5" expression="#[payload.error.errorMessage]" is="#[MunitTools::equalTo('The method specified in the request is not allowed for this resource')]" message="The response payload is not correct!" />
		</munit:validation>
	</munit:test>
			    <munit:test name="munit-global-not-acceptable-response-sub-flow" description="Verifying functionality of global-not-acceptable-response-sub-flow" doc:id="9a72ec85-b996-4162-9bc7-9bdebac40b68">
		<munit:enable-flow-sources >
			<munit:enable-flow-source value="prc-partners-api-main" />
			<munit:enable-flow-source value="get:\employee:prc-partners-config" />
		</munit:enable-flow-sources>
		<munit:execution>
			<http:request method="GET" doc:name="HTTP Request" doc:id="8068eed5-9adb-4e53-a650-81c89cba3ce5" config-ref="HTTP_Request_Configuration" path="/employee">
				<http:headers><![CDATA[#[output application/java
---
{
	"Accept" : "application/xml",
	"client-id" : "client-id",
	"client-secret" : "client-secret"
}]]]></http:headers>
				<http:response-validator >
					<http:success-status-code-validator values="300..500" />
				</http:response-validator>
			</http:request>
		</munit:execution>
		<munit:validation >
			<munit-tools:assert-that doc:name="Assert That Status Code is 406" doc:id="f0de0223-81d6-4146-97ac-654371f08741" expression="#[attributes.statusCode]" is="#[MunitTools::equalTo(406)]" message="The HTTP Status code is not correct!" />
			<munit-tools:assert-that doc:name="Assert That - Payload is Expected" doc:id="71da853e-1a27-4870-9fe8-a85c05d67076" expression="#[payload.error.errorMessage]" is="#[MunitTools::equalTo('The resource identified by the request is not capable of generating response entities according to the request accept headers')]" message="The response payload is not correct!" />
		</munit:validation>
	</munit:test>
   <!-- Negative Test Cases End --> 
     <munit:test name="get:\stats:api-config-ValidTest"
		description="Verifying functionality of [get:\stats:dc-sample-config-200-application\json]">
		<munit:enable-flow-sources>
			<munit:enable-flow-source value="prc-partners-api-main" />
			<munit:enable-flow-source value="get:\stats:prc-partners-config" />
		</munit:enable-flow-sources>
		<munit:execution>
			<http:request method="GET" doc:name="InvokeStatsFlow"
				doc:id="2e87ad00-6221-4001-b724-03e5a108f92a" config-ref="HTTP_Request_Configuration"
				path="/stats" outputMimeType="application/json">
				<http:headers><![CDATA[#[output application/java
---
{
	"Content-Type" : "application/json",
	"client-id" : "client-id",
	"client-secret" : "client-secret"
}]]]></http:headers>
			</http:request>
		</munit:execution>
		<munit:validation>
			<munit-tools:assert-that expression="#[attributes.statusCode]"
				is="#[MunitTools::equalTo(200)]" message="The HTTP Status code is not correct!"
				doc:name="Assert That Status Code is 200" />
			<munit-tools:assert-that doc:name="AssertNotNullPayload"
				doc:id="de560921-2c44-46f4-8e92-758debf59beb" expression="#[payload]"
				is="#[MunitTools::notNullValue()]" message="The output message is not correct" />
		</munit:validation>
	</munit:test>
    <!--  Health Check Endpoint Test Case Start -->
   <munit:test name="get:\healthcheck:api-config-ValidTest" description="Verifying functionality of [get:\healthcheck:employee-sample-config-200-application\json]">
        <munit:enable-flow-sources>
			<munit:enable-flow-source value="prc-partners-api-main" />
			<munit:enable-flow-source value="get:\healthcheck:prc-partners-config" />
		</munit:enable-flow-sources>
        <munit:execution>
			<http:request method="GET" doc:name="InvokeHealthCheckFlow" doc:id="61d5ddcc-55ea-4a3f-9603-2e295a936d7d" config-ref="HTTP_Request_Configuration" path="/healthcheck"/>
        </munit:execution>
        <munit:validation>
            <munit-tools:assert-that expression="#[attributes.statusCode]" is="#[MunitTools::equalTo(200)]" message="The HTTP Status code is not correct!" doc:name="Assert That Status Code is 200" />
            <munit-tools:assert-that expression="#[payload]" is="#[MunitTools::equalTo('prc-partners is alive and kicking.')]" message="The response payload is not correct!" doc:name="Assert That - Payload is Expected" />
        </munit:validation>
    </munit:test>
    <!--  Health Check Endpoint Test Case End -->
    
    
</mule>
