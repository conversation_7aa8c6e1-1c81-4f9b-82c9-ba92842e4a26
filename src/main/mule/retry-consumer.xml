<?xml version="1.0" encoding="UTF-8"?>

<mule xmlns:kafka="http://www.mulesoft.org/schema/mule/kafka"
	xmlns:ee="http://www.mulesoft.org/schema/mule/ee/core"
	xmlns="http://www.mulesoft.org/schema/mule/core" 
	xmlns:doc="http://www.mulesoft.org/schema/mule/documentation"
	xmlns:epdi-json-logger="http://www.mulesoft.org/schema/mule/epdi-json-logger"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
	xsi:schemaLocation="
http://www.mulesoft.org/schema/mule/ee/core http://www.mulesoft.org/schema/mule/ee/core/current/mule-ee.xsd 
http://www.mulesoft.org/schema/mule/core http://www.mulesoft.org/schema/mule/core/current/mule.xsd
http://www.mulesoft.org/schema/mule/epdi-json-logger http://www.mulesoft.org/schema/mule/epdi-json-logger/current/mule-epdi-json-logger.xsd
http://www.mulesoft.org/schema/mule/kafka http://www.mulesoft.org/schema/mule/kafka/current/mule-kafka.xsd">

	<flow name="retry-consumer-mainFlow" doc:id="retry-consumer-main-flow">
		<kafka:batch-message-listener doc:name="Retry Batch message listener" doc:id="retry-batch-message-listener" ackMode="MANUAL_IMMEDIATE" pollTimeout="5" pollTimeoutTimeUnit="SECONDS" config-ref="Apache_Kafka_Retry_Consumer_configuration"/>

		<choice doc:name="Is Retry Batch Valid?" doc:id="retry-batch-validation">
			<when expression="#[payload != null and not isEmpty(payload) and sizeOf(payload) > 0]">
				<set-variable value="#[payload.*attributes.consumerCommitKey[0]]" doc:name="Store retry commit key" doc:id="store-retry-commit-key" variableName="retryCommitKey"/>

				<foreach doc:name="Process Each Retry Message" doc:id="process-retry-messages" collection="#[payload]">
					<try doc:name="Try Retry Processing" doc:id="try-retry-processing">
						<ee:transform doc:name="Extract Retry Message Data" doc:id="extract-retry-message-data">
							<ee:message>
								<ee:set-payload><![CDATA[%dw 2.0
output application/json
var retryMessage = read(payload.payload, "application/json")
---
retryMessage.originalMessage]]></ee:set-payload>
							</ee:message>
							<ee:variables>
								<ee:set-variable variableName="retryMetadata"><![CDATA[%dw 2.0
output application/json
var retryMessage = read(payload.payload, "application/json")
---
retryMessage.retryMetadata]]></ee:set-variable>
								<ee:set-variable variableName="originalKafkaMessage"><![CDATA[%dw 2.0
output application/json
var retryMessage = read(payload.payload, "application/json")
---
retryMessage.originalMessage]]></ee:set-variable>
								<ee:set-variable variableName="currentRetryAttempt"><![CDATA[%dw 2.0
output application/json
var retryMessage = read(payload.payload, "application/json")
---
retryMessage.retryMetadata.attemptNumber]]></ee:set-variable>
							</ee:variables>
						</ee:transform>
						<choice doc:name="Check Retry Logic" doc:id="check-retry-logic">
							<when expression="#[vars.currentRetryAttempt > (p('kafka.consumer.retry.max-attempts') as Number)]">
								<flow-ref doc:name="dlq-handlerSub_Flow" doc:id="max-attempts-to-dlq" name="dlq-handlerSub_Flow"/>
							</when>
							<when expression="#[now() >= (vars.retryMetadata.nextRetryTime as DateTime)]">
								<flow-ref doc:name="brand-to-osapiens-processingSub_Flow" doc:id="retry-processing-flow-ref" name="brand-to-osapiens-processingSub_Flow"/>
							</when>
							<otherwise>
								<flow-ref doc:name="retry-handlerSub_Flow" doc:id="requeue-retry-message" name="retry-handlerSub_Flow"/>
							</otherwise>
						</choice>
						<error-handler>
							<on-error-continue enableNotifications="true" logException="true" doc:name="Handle retry processing errors" doc:id="retry-processing-error-handler">
								<choice doc:name="Check Retry Attempts on Error" doc:id="check-retry-attempts-on-error">
									<when expression="#[vars.currentRetryAttempt >= (p('kafka.consumer.retry.max-attempts') as Number)]">
										<flow-ref doc:name="dlq-handlerSub_Flow" doc:id="retry-failed-to-dlq" name="dlq-handlerSub_Flow"/>
									</when>
									<otherwise>
										<flow-ref doc:name="retry-handlerSub_Flow" doc:id="retry-again-on-error" name="retry-handlerSub_Flow"/>
									</otherwise>
								</choice>
							</on-error-continue>
						</error-handler>
					</try>
				</foreach>

				<kafka:commit doc:name="Commit retry batch" doc:id="commit-retry-batch" config-ref="Apache_Kafka_Retry_Consumer_configuration" commitKey="#[vars.retryCommitKey]"/>
			</when>
			<otherwise>
				<set-variable value="#[payload.*attributes.consumerCommitKey[0]]" doc:name="Store invalid retry commit key" doc:id="store-invalid-retry-commit-key" variableName="invalidRetryCommitKey"/>
				<kafka:commit doc:name="Commit invalid retry batch" doc:id="commit-invalid-retry-batch" config-ref="Apache_Kafka_Retry_Consumer_configuration" commitKey="#[vars.invalidRetryCommitKey]"/>
			</otherwise>
		</choice>
	</flow>
</mule>
