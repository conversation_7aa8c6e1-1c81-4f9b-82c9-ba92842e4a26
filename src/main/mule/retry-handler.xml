<?xml version="1.0" encoding="UTF-8"?>

<mule xmlns:kafka="http://www.mulesoft.org/schema/mule/kafka"
	xmlns:ee="http://www.mulesoft.org/schema/mule/ee/core"
	xmlns="http://www.mulesoft.org/schema/mule/core" 
	xmlns:doc="http://www.mulesoft.org/schema/mule/documentation"
	xmlns:epdi-json-logger="http://www.mulesoft.org/schema/mule/epdi-json-logger"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
	xsi:schemaLocation="
http://www.mulesoft.org/schema/mule/ee/core http://www.mulesoft.org/schema/mule/ee/core/current/mule-ee.xsd 
http://www.mulesoft.org/schema/mule/core http://www.mulesoft.org/schema/mule/core/current/mule.xsd
http://www.mulesoft.org/schema/mule/epdi-json-logger http://www.mulesoft.org/schema/mule/epdi-json-logger/current/mule-epdi-json-logger.xsd
http://www.mulesoft.org/schema/mule/kafka http://www.mulesoft.org/schema/mule/kafka/current/mule-kafka.xsd">

	<sub-flow name="retry-handlerSub_Flow" doc:id="retry-handler-sub-flow">
		<ee:transform doc:name="Prepare Retry Message" doc:id="prepare-retry-message">
			<ee:message>
				<ee:set-payload><![CDATA[%dw 2.0
output application/json
---
{
	"originalMessage": vars.originalKafkaMessage,
	"httpError": {
		"statusCode": vars.httpStatusCode,
		"errorMessage": vars.httpErrorMessage,
		"errorType": vars.httpErrorType
	},
	"retryMetadata": {
		"timestamp": now(),
		"correlationId": vars.trackingVar.'correlationId' default correlationId,
		"source": "prc-partners",
		"retryReason": "downstream_5xx_error",
		"firstRetryTime": vars.firstRetryTime default now(),
		"retryWindowExpires": (vars.firstRetryTime default now()) + |PT40M|,
		"attemptNumber": (vars.currentRetryAttempt default 0) + 1,
		"maxAttempts": p('kafka.consumer.retry.max-attempts') as Number
	}
}]]></ee:set-payload>
			</ee:message>
		</ee:transform>
		
		<try doc:name="Try Retry Publish" doc:id="try-retry-publish">
			<kafka:publish doc:name="Publish to Retry Topic" doc:id="publish-to-retry" config-ref="Apache_Kafka_Producer_configuration" topic="${kafka.consumer.retry.topic}" key="#[vars.originalKafkaMessage.businessPartnerId as String]"/>
			<error-handler>
				<on-error-continue enableNotifications="true" logException="true" doc:name="Handle Retry publish errors" doc:id="retry-publish-error-handler">
				</on-error-continue>
			</error-handler>
		</try>
	</sub-flow>
</mule>
