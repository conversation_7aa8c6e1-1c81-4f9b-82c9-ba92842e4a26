<?xml version="1.0" encoding="UTF-8"?>
<mule
	xmlns:epdi-json-logger="http://www.mulesoft.org/schema/mule/epdi-json-logger"
	xmlns:ee="http://www.mulesoft.org/schema/mule/ee/core"
	xmlns="http://www.mulesoft.org/schema/mule/core"
	xmlns:apikit="http://www.mulesoft.org/schema/mule/mule-apikit"
	xmlns:http="http://www.mulesoft.org/schema/mule/http"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:doc="http://www.mulesoft.org/schema/mule/documentation" xsi:schemaLocation="http://www.mulesoft.org/schema/mule/core http://www.mulesoft.org/schema/mule/core/current/mule.xsd http://www.mulesoft.org/schema/mule/http http://www.mulesoft.org/schema/mule/http/current/mule-http.xsd http://www.mulesoft.org/schema/mule/mule-apikit http://www.mulesoft.org/schema/mule/mule-apikit/current/mule-apikit.xsd 
http://www.mulesoft.org/schema/mule/ee/core http://www.mulesoft.org/schema/mule/ee/core/current/mule-ee.xsd
http://www.mulesoft.org/schema/mule/epdi-json-logger http://www.mulesoft.org/schema/mule/epdi-json-logger/current/mule-epdi-json-logger.xsd">
	<flow name="prc-partners-api-main">
		<http:listener config-ref="httpListenerConfig"
			path="${api.http.listener.path}">
			<http:response
				statusCode="#[vars.httpStatus default 200]">
				<http:headers><![CDATA[#[output application/java
---
{
	"Strict-Transport-Security": "max-age=15552000; preload",
	"Cache-Control": "private, no-cache, no-store, must-revalidate",
	"Pragma": "no-cache",
	"X-Content-Type-Options": "nosniff",
	"Referrer-Policy": "no-referrer",
	"X-Frame-Options": "DENY",
	"Content-Security-Policy": "default-src 'self'",
	"X-XSS-Protection": "1; mode=block",
	"Feature-Policy": "geolocation 'self'",
	"X-Permitted-Cross-Domain-Policies": "none"
}]]]></http:headers>
			</http:response>
			<http:error-response
				statusCode="#[vars.httpStatus default 500]">
				<http:body><![CDATA[#[payload]]]></http:body>
				<http:headers ><![CDATA[#[output application/java
---
{
	"Strict-Transport-Security": "max-age=15552000; preload",
	"Cache-Control": "private, no-cache, no-store, must-revalidate",
	"Pragma": "no-cache",
	"X-Content-Type-Options": "nosniff",
	"Referrer-Policy": "no-referrer",
	"X-Frame-Options": "DENY",
	"Content-Security-Policy": "default-src 'self'",
	"X-XSS-Protection": "1; mode=block",
	"Feature-Policy": "geolocation 'self'",
	"X-Permitted-Cross-Domain-Policies": "none"
}]]]></http:headers>
			</http:error-response>
		</http:listener>
		<flow-ref doc:name="fetchTrackingValues"
			doc:id="ec0018e7-754d-4d50-8e30-2bcd315c07d4"
			name="prc-partners-trackingvars-subflow" />
		<set-variable
			value="#[(attributes.requestUri endsWith 'healthcheck') == true]"
			doc:name="Is Healthcheck?"
			doc:id="6062afb2-c801-461b-b723-4dd22f0e60be"
			variableName="isHealthCheck" />
		<choice doc:name="Choice"
			doc:id="dfb26fb8-476e-4965-90d9-4eb4e68c4d21">
			<when expression="#[vars.isHealthCheck]">
				<set-payload value="#[payload]" doc:name="Passthrough"
					doc:id="e94587b9-a0cf-4acd-a2c7-55bd3f53a01b" />
			</when>
			<otherwise>
				<logger level="INFO" doc:name="Logger"
					doc:id="bc9ae751-0c17-4ae4-b09b-58bc7fe19155"
					message="Transaction [#[vars.trackingVar.transactionId]] - Resource [#[attributes.method] #[attributes.requestUri]]" />
			</otherwise>
		</choice>
		<apikit:router config-ref="prc-partners-config"
			doc:name="Router" />
		<choice doc:name="Choice"
			doc:id="7d6d2d04-5706-4c15-8b79-06d4273f74b9">
			<when
				expression="#[vars.isHealthCheck == true or (vars.errorRaised != null and vars.errorRaised == true)]">
				<set-payload value="#[payload]" doc:name="Passthrough"
					doc:id="a7ed04ae-2d39-444d-9a18-af788dd77162" />
			</when>
			<otherwise>
				<logger level="INFO" doc:name="Logger"
					doc:id="d84bcdc7-3a0d-42d2-843d-b28f5c3a39e5"
					message="Transaction [#[vars.trackingVar.transactionId]] - Returned Success Response to Client" />
			</otherwise>
		</choice>
		<error-handler ref="global-error-handler"
			doc:name="Reference Exception Strategy"
			doc:description="The Global Exception Strategy" />
	</flow>
	<flow name="prc-partners-api-console">
		<http:listener config-ref="httpListenerConfig"
			path="${api.http.listener.console}">
			<http:response
				statusCode="#[vars.httpStatus default 200]">
				<http:headers><![CDATA[#[output application/java
---
{
	"Strict-Transport-Security" : "max-age=15552000; preload",
	"Cache-Control": "private, no-cache, no-store, must-revalidate",
"Pragma": "no-cache",
"X-Content-Type-Options": "nosniff",
"Referrer-Policy": "no-referrer",
"X-Frame-Options": "DENY"
}]]]></http:headers>
			</http:response>
			<http:error-response
				statusCode="#[vars.httpStatus default 500]">
				<http:body><![CDATA[#[payload]]]></http:body>
			</http:error-response>
		</http:listener>
		<flow-ref doc:name="fetchTrackingValues"
			doc:id="40dc5099-1501-4384-99cd-40d47f0bc68f"
			name="prc-partners-trackingvars-subflow" />
		<apikit:console config-ref="prc-partners-config"
			doc:name="Generate Documentation Console" />
		<async doc:name="logToELK" doc:id="527071b5-eb43-454c-995c-174cc6ad11fa" >
			<epdi-json-logger:trace-it MCV="P4" OName="BECSE" DName="Retail" category="functionalTrace" doc:name="Trace it" doc:id="f942307f-93fd-4b0b-93d5-692bdfde76ac" transactionStatusMessage="#['Success']" payload='#[write({"message": "API Console Trigger was success"}, "application/json")]'/>
		</async>
		
		<error-handler ref="global-error-handler"
			doc:name="Reference Exception Strategy"
			doc:description="The Global Exception Strategy" />
	</flow>
	<sub-flow name="prc-partners-trackingvars-subflow"
		doc:id="551621f0-8e03-4c87-90b4-4f5d5260f5c5">
		<ee:transform doc:name="trackParameters"
			doc:id="25a64edc-cd1d-4d8b-8fba-f8efd322a678">
			<ee:message>
			</ee:message>
			<ee:variables>
				<ee:set-variable
					resource="requestMappings/trackingVars.dwl"
					variableName="trackingVar" />
			</ee:variables>
		</ee:transform>
	</sub-flow>


</mule>
