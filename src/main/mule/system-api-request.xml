<?xml version="1.0" encoding="UTF-8"?>

<mule xmlns:ee="http://www.mulesoft.org/schema/mule/ee/core"
	xmlns:http="http://www.mulesoft.org/schema/mule/http" xmlns="http://www.mulesoft.org/schema/mule/core"
	xmlns:doc="http://www.mulesoft.org/schema/mule/documentation"
    xmlns:epdi-json-logger="http://www.mulesoft.org/schema/mule/epdi-json-logger"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="
http://www.mulesoft.org/schema/mule/ee/core http://www.mulesoft.org/schema/mule/ee/core/current/mule-ee.xsd
http://www.mulesoft.org/schema/mule/core http://www.mulesoft.org/schema/mule/core/current/mule.xsd
http://www.mulesoft.org/schema/mule/epdi-json-logger http://www.mulesoft.org/schema/mule/epdi-json-logger/current/mule-epdi-json-logger.xsd
http://www.mulesoft.org/schema/mule/http http://www.mulesoft.org/schema/mule/http/current/mule-http.xsd">
	<sub-flow name="system-api-requestSub_Flow" doc:id="0c3aedc4-f09b-411f-9add-91f6b3895a7d" >
		<choice doc:name="Is Payload Valid for API Call?" doc:id="a1b2c3d4-e5f6-7890-1234-567890abcdef">
			<when expression="#[payload != null and not isEmpty(payload) and vars.httpRequest != null and vars.httpRequest.method != null and vars.httpRequest.path != null and vars.httpRequest.message != null]">
                <epdi-json-logger:trace-it MCV="P4" DName="Retail" category="functionalTrace" doc:name="Trace it" doc:id="k10005c9-40cf-4736-96c0-0f61f7927eff" OName="BECSE" correlationId="#[vars.trackingVar.'correlationId' default correlationId]" transactionStatusMessage="(prc-partners) to (sys-osapiens-hub) before request" payload='#[write({
					"httpRequest": vars.httpRequest,
					"payload": payload
				}, "application/json")]'/>
				<try doc:name="Try HTTP Request" doc:id="http-request-try-block">
					<ee:transform doc:name="Set Payload" doc:id="c971e7zz-8ccb-45bd-9a85-2317d78bf115">
						<ee:message>
							<ee:set-payload><![CDATA[%dw 2.0
output application/json
---
vars.httpRequest.message]]></ee:set-payload>
						</ee:message>
					</ee:transform>
					<http:request method="#[vars.httpRequest.method]" doc:name="Call System API" doc:id="7c33644e-d0e8-467e-b8cb-57267d25f846" path="#[vars.httpRequest.path]" sendCorrelationId="ALWAYS" correlationId="#[vars.trackingVar.'correlationId' default correlationId]" config-ref="HTTP_Request_configuration_sys"></http:request>

					<!-- Store HTTP response attributes for error handling -->
					<set-variable value="#[attributes.statusCode]" doc:name="Store HTTP Status Code" doc:id="store-http-status-code" variableName="httpStatusCode"/>

			        <epdi-json-logger:trace-it MCV="P4" DName="Retail" category="functionalTrace" doc:name="Trace it" doc:id="s21006c3-40cf-4736-96c0-0f61f7927eff" OName="BECSE" correlationId="#[vars.trackingVar.'correlationId' default correlationId]" transactionStatusMessage="(prc-partners) to (sys-osapiens-hub) after request" payload='#[write({
						"response": payload default "empty response",
						"statusCode": vars.httpStatusCode
					}, "application/json")]'/>

					<choice doc:name="Check Downstream Response" doc:id="check-downstream-response">
						<when expression="#[payload[0].statusCode == 'error' and payload[0].errorCode != null]">
							<epdi-json-logger:trace-it MCV="P4" DName="Retail" category="errorTrace" doc:name="Trace it" doc:id="downstream-error-trace" OName="BECSE" correlationId="#[vars.trackingVar.'correlationId' default correlationId]" transactionStatusMessage="(prc-partners) to (sys-osapiens-hub) downstream system returned error - sending to DLQ" payload='#[write({
								"downstreamResponse": payload,
								"originalMessage": vars.httpRequest.message,
								"errorCode": payload.errorCode,
								"errorMessage": payload.message
							}, "application/json")]'/>
							<flow-ref doc:name="dlq-handlerSub_Flow" doc:id="call-dlq-handler" name="dlq-handlerSub_Flow"/>
						</when>
						<otherwise>
							<epdi-json-logger:trace-it MCV="P4" DName="Retail" category="functionalTrace" doc:name="Trace it" doc:id="downstream-success-trace" OName="BECSE" correlationId="#[vars.trackingVar.'correlationId' default correlationId]" transactionStatusMessage="(prc-partners) to (sys-osapiens-hub) downstream system processed successfully"/>
						</otherwise>
					</choice>
					<error-handler>
						<on-error-continue enableNotifications="true" logException="true" doc:name="Handle HTTP 4xx errors" doc:id="http-4xx-error-handler" type="HTTP:CLIENT_ERROR">
							<!-- Handle 4xx errors - send to DLQ -->
							<set-variable value="#[error.errorMessage.attributes.statusCode]" doc:name="Store 4xx Status Code" doc:id="store-4xx-status-code" variableName="httpStatusCode"/>
							<set-variable value="#[error.errorMessage.payload]" doc:name="Store 4xx Error Message" doc:id="store-4xx-error-message" variableName="httpErrorMessage"/>
							<set-variable value="#[error.errorType]" doc:name="Store 4xx Error Type" doc:id="store-4xx-error-type" variableName="httpErrorType"/>

							<epdi-json-logger:trace-it MCV="P4" DName="Retail" category="errorTrace" doc:name="Trace it" doc:id="http-4xx-error-trace" OName="BECSE" correlationId="#[vars.trackingVar.'correlationId' default correlationId]" transactionStatusMessage="(prc-partners) to (sys-osapiens-hub) 4xx HTTP error - sending to DLQ" payload='#[write({
								"error": {
									"type": if (error.errorType != null)
										(error.errorType.namespace default "") ++ ":" ++ (error.errorType.identifier default "")
									else
										"UNKNOWN",
									"description": error.description default "No description available",
									"statusCode": vars.httpStatusCode,
									"errorMessage": vars.httpErrorMessage
								},
								"httpRequest": vars.httpRequest,
								"originalPayload": vars.httpRequest.message
							}, "application/json")]'/>
							<flow-ref doc:name="dlq-handlerSub_Flow" doc:id="call-dlq-handler-4xx" name="dlq-handlerSub_Flow"/>
						</on-error-continue>
						<on-error-continue enableNotifications="true" logException="true" doc:name="Handle HTTP 5xx errors" doc:id="http-5xx-error-handler" type="HTTP:SERVER_ERROR">
							<!-- Handle 5xx errors - send to retry queue -->
							<set-variable value="#[error.errorMessage.attributes.statusCode]" doc:name="Store 5xx Status Code" doc:id="store-5xx-status-code" variableName="httpStatusCode"/>
							<set-variable value="#[error.errorMessage.payload]" doc:name="Store 5xx Error Message" doc:id="store-5xx-error-message" variableName="httpErrorMessage"/>
							<set-variable value="#[error.errorType]" doc:name="Store 5xx Error Type" doc:id="store-5xx-error-type" variableName="httpErrorType"/>

							<epdi-json-logger:trace-it MCV="P4" DName="Retail" category="errorTrace" doc:name="Trace it" doc:id="http-5xx-error-trace" OName="BECSE" correlationId="#[vars.trackingVar.'correlationId' default correlationId]" transactionStatusMessage="(prc-partners) to (sys-osapiens-hub) 5xx HTTP error - sending to retry queue" payload='#[write({
								"error": {
									"type": if (error.errorType != null)
										(error.errorType.namespace default "") ++ ":" ++ (error.errorType.identifier default "")
									else
										"UNKNOWN",
									"description": error.description default "No description available",
									"statusCode": vars.httpStatusCode,
									"errorMessage": vars.httpErrorMessage
								},
								"httpRequest": vars.httpRequest,
								"originalPayload": vars.httpRequest.message
							}, "application/json")]'/>
							<flow-ref doc:name="retry-handlerSub_Flow" doc:id="call-retry-handler-5xx" name="retry-handlerSub_Flow"/>
						</on-error-continue>
						<on-error-continue enableNotifications="true" logException="true" doc:name="Handle other HTTP errors" doc:id="http-other-error-handler">
							<!-- Handle other HTTP errors (connectivity, timeout, etc.) - send to retry queue -->
							<set-variable value="#[500]" doc:name="Set default status code" doc:id="set-default-status-code" variableName="httpStatusCode"/>
							<set-variable value="#[error.description]" doc:name="Store other error message" doc:id="store-other-error-message" variableName="httpErrorMessage"/>
							<set-variable value="#[error.errorType]" doc:name="Store other error type" doc:id="store-other-error-type" variableName="httpErrorType"/>

							<epdi-json-logger:trace-it MCV="P4" DName="Retail" category="errorTrace" doc:name="Trace it" doc:id="http-other-error-trace" OName="BECSE" correlationId="#[vars.trackingVar.'correlationId' default correlationId]" transactionStatusMessage="(prc-partners) to (sys-osapiens-hub) connectivity/other HTTP error - sending to retry queue" payload='#[write({
								"error": {
									"type": if (error.errorType != null)
										(error.errorType.namespace default "") ++ ":" ++ (error.errorType.identifier default "")
									else
										"UNKNOWN",
									"description": error.description default "No description available",
									"statusCode": vars.httpStatusCode,
									"errorMessage": vars.httpErrorMessage
								},
								"httpRequest": vars.httpRequest,
								"originalPayload": vars.httpRequest.message
							}, "application/json")]'/>
							<flow-ref doc:name="retry-handlerSub_Flow" doc:id="call-retry-handler-other" name="retry-handlerSub_Flow"/>
						</on-error-continue>
					</error-handler>
				</try>
			</when>
			<otherwise>
				<epdi-json-logger:trace-it MCV="P4" DName="Retail" category="functionalTrace" doc:name="Trace it" doc:id="s21006c3-40cf-4736-96c0-0f61f7927eaa" OName="BECSE" correlationId="#[vars.trackingVar.'correlationId' default correlationId]" transactionStatusMessage="(prc-partners) to (sys-osapiens-hub) skipped - invalid payload or request" payload='#[write({
					"payload": payload default "null",
					"httpRequest": vars.httpRequest default "null",
					"reason": if (payload == null or isEmpty(payload)) "empty_payload"
							  else if (vars.httpRequest == null) "missing_http_request"
							  else if (vars.httpRequest.method == null) "missing_http_method"
							  else if (vars.httpRequest.path == null) "missing_http_path"
							  else if (vars.httpRequest.message == null) "missing_http_message"
							  else "unknown"
				}, "application/json")]'/>
			</otherwise>
		</choice>
	</sub-flow>
</mule>
