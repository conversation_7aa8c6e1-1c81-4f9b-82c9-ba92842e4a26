<?xml version="1.0" encoding="UTF-8"?>

<mule xmlns:ee="http://www.mulesoft.org/schema/mule/ee/core"
	xmlns:http="http://www.mulesoft.org/schema/mule/http" xmlns="http://www.mulesoft.org/schema/mule/core"
	xmlns:doc="http://www.mulesoft.org/schema/mule/documentation"
    xmlns:epdi-json-logger="http://www.mulesoft.org/schema/mule/epdi-json-logger"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="
http://www.mulesoft.org/schema/mule/ee/core http://www.mulesoft.org/schema/mule/ee/core/current/mule-ee.xsd
http://www.mulesoft.org/schema/mule/core http://www.mulesoft.org/schema/mule/core/current/mule.xsd
http://www.mulesoft.org/schema/mule/epdi-json-logger http://www.mulesoft.org/schema/mule/epdi-json-logger/current/mule-epdi-json-logger.xsd
http://www.mulesoft.org/schema/mule/http http://www.mulesoft.org/schema/mule/http/current/mule-http.xsd">
	<sub-flow name="system-api-requestSub_Flow" doc:id="0c3aedc4-f09b-411f-9add-91f6b3895a7d" >
		<choice doc:name="Is Payload Valid for API Call?" doc:id="a1b2c3d4-e5f6-7890-1234-567890abcdef">
			<when expression="#[payload != null and not isEmpty(payload) and vars.httpRequest != null and vars.httpRequest.method != null and vars.httpRequest.path != null and vars.httpRequest.message != null]">
                <epdi-json-logger:trace-it MCV="P4" DName="Retail" category="functionalTrace" doc:name="Trace it" doc:id="k10005c9-40cf-4736-96c0-0f61f7927eff" OName="BECSE" correlationId="#[vars.trackingVar.'correlationId' default correlationId]" transactionStatusMessage="(prc-partners) to (sys-osapiens-hub) before request" payload='#[write({
					"httpRequest": vars.httpRequest,
					"payload": payload
				}, "application/json")]'/>
				<try doc:name="Try HTTP Request" doc:id="http-request-try-block">
					<ee:transform doc:name="Set Payload" doc:id="c971e7zz-8ccb-45bd-9a85-2317d78bf115">
						<ee:message>
							<ee:set-payload><![CDATA[%dw 2.0
output application/json
---
vars.httpRequest.message]]></ee:set-payload>
						</ee:message>
					</ee:transform>
					<http:request method="#[vars.httpRequest.method]" doc:name="Call System API" doc:id="7c33644e-d0e8-467e-b8cb-57267d25f846" path="#[vars.httpRequest.path]" sendCorrelationId="ALWAYS" correlationId="#[vars.trackingVar.'correlationId' default correlationId]" config-ref="HTTP_Request_configuration_sys"></http:request>
			        <epdi-json-logger:trace-it MCV="P4" DName="Retail" category="functionalTrace" doc:name="Trace it" doc:id="s21006c3-40cf-4736-96c0-0f61f7927eff" OName="BECSE" correlationId="#[vars.trackingVar.'correlationId' default correlationId]" transactionStatusMessage="(prc-partners) to (sys-osapiens-hub) after request" payload='#[write(payload default "empty response", "application/json")]'/>

					<choice doc:name="Check Downstream Response" doc:id="check-downstream-response">
						<when expression="#[payload[0].statusCode == 'error' and payload[0].errorCode != null]">
							<epdi-json-logger:trace-it MCV="P4" DName="Retail" category="errorTrace" doc:name="Trace it" doc:id="downstream-error-trace" OName="BECSE" correlationId="#[vars.trackingVar.'correlationId' default correlationId]" transactionStatusMessage="(prc-partners) to (sys-osapiens-hub) downstream system returned error - sending to DLQ" payload='#[write({
								"downstreamResponse": payload,
								"originalMessage": vars.httpRequest.message,
								"errorCode": payload.errorCode,
								"errorMessage": payload.message
							}, "application/json")]'/>
							<flow-ref doc:name="dlq-handlerSub_Flow" doc:id="call-dlq-handler" name="dlq-handlerSub_Flow"/>
						</when>
						<otherwise>
							<epdi-json-logger:trace-it MCV="P4" DName="Retail" category="functionalTrace" doc:name="Trace it" doc:id="downstream-success-trace" OName="BECSE" correlationId="#[vars.trackingVar.'correlationId' default correlationId]" transactionStatusMessage="(prc-partners) to (sys-osapiens-hub) downstream system processed successfully"/>
						</otherwise>
					</choice>
					<error-handler>
						<on-error-continue enableNotifications="true" logException="true" doc:name="Handle HTTP errors" doc:id="http-error-handler">
							<epdi-json-logger:trace-it MCV="P4" DName="Retail" category="errorTrace" doc:name="Trace it" doc:id="http-error-trace" OName="BECSE" correlationId="#[vars.trackingVar.'correlationId' default correlationId]" transactionStatusMessage="(prc-partners) to (sys-osapiens-hub) HTTP error" payload='#[write({
								"error": {
									"type": if (error.errorType != null) 
										(error.errorType.namespace default "") ++ ":" ++ (error.errorType.identifier default "") 
										else "UNKNOWN",
									"namespace": error.errorType.namespace default null,
									"identifier": error.errorType.identifier default null,
									"description": error.description default "No description available",
									"detailedDescription": error.detailedDescription default "No detailed description available",
									"cause": if (error.cause != null) error.cause.toString() default "No cause available" else "No cause available"
								},
								"httpRequest": vars.httpRequest,
								"originalPayload": payload default "No payload available"
							}, "application/json")]'/>
						</on-error-continue>
					</error-handler>
				</try>
			</when>
			<otherwise>
				<epdi-json-logger:trace-it MCV="P4" DName="Retail" category="functionalTrace" doc:name="Trace it" doc:id="s21006c3-40cf-4736-96c0-0f61f7927eaa" OName="BECSE" correlationId="#[vars.trackingVar.'correlationId' default correlationId]" transactionStatusMessage="(prc-partners) to (sys-osapiens-hub) skipped - invalid payload or request" payload='#[write({
					"payload": payload default "null",
					"httpRequest": vars.httpRequest default "null",
					"reason": if (payload == null or isEmpty(payload)) "empty_payload"
							  else if (vars.httpRequest == null) "missing_http_request"
							  else if (vars.httpRequest.method == null) "missing_http_method"
							  else if (vars.httpRequest.path == null) "missing_http_path"
							  else if (vars.httpRequest.message == null) "missing_http_message"
							  else "unknown"
				}, "application/json")]'/>
			</otherwise>
		</choice>
	</sub-flow>
</mule>
