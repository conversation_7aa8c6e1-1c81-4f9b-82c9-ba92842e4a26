<?xml version="1.0" encoding="UTF-8"?>

<mule xmlns:ee="http://www.mulesoft.org/schema/mule/ee/core"
    xmlns:kafka="http://www.mulesoft.org/schema/mule/kafka"
    xmlns="http://www.mulesoft.org/schema/mule/core"
    xmlns:epdi-json-logger="http://www.mulesoft.org/schema/mule/epdi-json-logger"
    xmlns:doc="http://www.mulesoft.org/schema/mule/documentation"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.mulesoft.org/schema/mule/core http://www.mulesoft.org/schema/mule/core/current/mule.xsd
                        http://www.mulesoft.org/schema/mule/kafka http://www.mulesoft.org/schema/mule/kafka/current/mule-kafka.xsd
                        http://www.mulesoft.org/schema/mule/epdi-json-logger http://www.mulesoft.org/schema/mule/epdi-json-logger/current/mule-epdi-json-logger.xsd
                        http://www.mulesoft.org/schema/mule/ee/core http://www.mulesoft.org/schema/mule/ee/core/current/mule-ee.xsd">
    <flow name="brand-to-osapiens-implementation-mainFlow" doc:id="95f571d1-f994-4793-bb33-4a4759800444">
        <kafka:batch-message-listener doc:name="Batch message listener" doc:id="4fa0ccc6-962c-45e0-95d9-dcf1d77421e2" ackMode="MANUAL" pollTimeout="5" pollTimeoutTimeUnit="SECONDS" config-ref="Apache_Kafka_Consumer_configuration" />
        <epdi-json-logger:trace-it MCV="P4" DName="Retail" category="functionalTrace" doc:name="Trace it" doc:id="b68265c9-40cf-4736-96c0-0f61f7927eff" OName="BECSE" correlationId="#[vars.trackingVar.'correlationId' default correlationId]" transactionStatusMessage="(Osapiens) Kafka Supplier Consumers - entry flow" payload='#[write({
			"batchSize": sizeOf(payload default []),
			"payloadType": typeOf(payload),
			"hasPayload": payload != null and not isEmpty(payload)
		}, "application/json")]' />
        <choice doc:name="Is Kafka Batch Valid?" doc:id="a1b2c3d4-e5f6-7890-1234-567890abcdef">
            <when expression="#[payload != null and not isEmpty(payload) and sizeOf(payload) > 0]">
                <epdi-json-logger:trace-it MCV="P4" DName="Retail" category="functionalTrace" doc:name="Trace it" doc:id="kafka-batch-valid-trace" OName="BECSE" correlationId="#[vars.trackingVar.'correlationId' default correlationId]" transactionStatusMessage="(Osapiens) Kafka Supplier Consumers - processing valid batch" payload='#[write({
					"batchSize": sizeOf(payload),
					"firstMessagePreview": if (sizeOf(payload) > 0) payload[0] else null
				}, "application/json")]' />
                <set-variable value="#[payload.*attributes.consumerCommitKey[0]]" doc:name="Store commit key" doc:id="store-commit-key" variableName="kafkaCommitKey"/>
                <try doc:name="Try Processing" doc:id="2f577e64-da9b-4ff5-b119-50e75a5646f8">
                    <flow-ref doc:name="brand-to-osapiens-processingSub_Flow" doc:id="28603d9e-19d2-4c41-80fc-48bcb4ab5260" name="brand-to-osapiens-processingSub_Flow" />
                    <kafka:commit doc:name="Commit successful processing" doc:id="kafka-commit-success" config-ref="Apache_Kafka_Consumer_configuration" commitKey="#[vars.kafkaCommitKey]" />
                    <epdi-json-logger:trace-it MCV="P4" DName="Retail" category="functionalTrace" doc:name="Trace it" doc:id="kafka-commit-success-trace" OName="BECSE" correlationId="#[vars.trackingVar.'correlationId' default correlationId]" transactionStatusMessage="(Osapiens) Kafka Supplier Consumers - batch processed and committed successfully" payload='#[write({
					"commitKey": vars.kafkaCommitKey
				}, "application/json")]' />
                    <error-handler>
                        <on-error-continue enableNotifications="true" logException="true" doc:name="Handle processing errors" doc:id="02b63bd6-f520-4347-9659-2846f68bbdfb">
                            <epdi-json-logger:trace-it MCV="P4" DName="Retail" category="errorTrace" doc:name="Trace it" doc:id="k54565r9-40hb-4736-96c0-0f61f7927eff" OName="BECSE" correlationId="#[vars.trackingVar.'correlationId' default correlationId]" transactionStatusMessage="(Osapiens) Kafka Supplier Consumers - PROCESSING ERROR" />
                        </on-error-continue>
                    </error-handler>
                </try>
            </when>
            <otherwise>
                <epdi-json-logger:trace-it MCV="P4" DName="Retail" category="functionalTrace" doc:name="Trace it" doc:id="b68265c9-45cf-4736-96c0-1f61f7927efz" OName="BECSE" correlationId="#[vars.trackingVar.'correlationId' default correlationId]" transactionStatusMessage="(Osapiens) Kafka Supplier Consumers - skipped invalid batch" payload='#[write({
					"reason": if (payload == null) "null_payload"
							  else if (isEmpty(payload)) "empty_payload"
							  else if (sizeOf(payload) == 0) "zero_size_batch"
							  else "unknown",
					"payloadType": typeOf(payload),
					"payloadSize": sizeOf(payload default [])
				}, "application/json")]' />
                <set-variable value="#[payload.*attributes.consumerCommitKey[0]]" doc:name="Store commit key for invalid batch" doc:id="store-commit-key-invalid" variableName="kafkaCommitKeyInvalid"/>
                <kafka:commit doc:name="Commit invalid batch" doc:id="kafka-commit-invalid" config-ref="Apache_Kafka_Consumer_configuration" commitKey="#[vars.kafkaCommitKeyInvalid]" />
                <epdi-json-logger:trace-it MCV="P4" DName="Retail" category="functionalTrace" doc:name="Trace it" doc:id="kafka-commit-invalid-trace" OName="BECSE" correlationId="#[vars.trackingVar.'correlationId' default correlationId]" transactionStatusMessage="(Osapiens) Kafka Supplier Consumers - invalid batch committed to avoid reprocessing" />
            </otherwise>
        </choice>
    </flow>
</mule>