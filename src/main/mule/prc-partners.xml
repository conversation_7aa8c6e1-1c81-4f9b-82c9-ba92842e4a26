<?xml version="1.0" encoding="UTF-8"?>

<mule
	xmlns:java="http://www.mulesoft.org/schema/mule/java" xmlns:ee="http://www.mulesoft.org/schema/mule/ee/core"
	xmlns="http://www.mulesoft.org/schema/mule/core"
	xmlns:doc="http://www.mulesoft.org/schema/mule/documentation" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="
http://www.mulesoft.org/schema/mule/ee/core http://www.mulesoft.org/schema/mule/ee/core/current/mule-ee.xsd http://www.mulesoft.org/schema/mule/core http://www.mulesoft.org/schema/mule/core/current/mule.xsd
http://www.mulesoft.org/schema/mule/java http://www.mulesoft.org/schema/mule/java/current/mule-java.xsd">
	<flow name="get:\employee:prc-partners-config">
		<ee:transform doc:name="Transform Message For Client" doc:id="2bc634b3-9b11-4002-817a-6d8495093a50" >


			<ee:message >
				<ee:set-payload resource="requestMappings/demo_map.dwl"/>
			</ee:message>
			</ee:transform>
	</flow>
	<flow name="get:\healthcheck:prc-partners-config">
        <set-payload value="#['&quot;' ++ ((p('api.name') splitBy ':')[-1] as String) ++ ' is alive and kicking.' ++ '&quot;']" doc:name="Set Payload" doc:id="a3569a07-1eb4-4633-8ea6-e23396260a41" mimeType="application/json"/>
    
</flow>
 <flow name="get:\stats:prc-partners-config">
        <logger level="INFO" doc:name="Logger" doc:id="79174983-494d-45b4-9a84-4ebb229f589d" message="Use Scatter Gather and write the logic to halthcheck all the applications involved. Use the mappings in statsUtilityMappings to define the final output"/>
		<java:new doc:name="InvokeSystemCheckUtility" doc:id="5b3aa069-eee8-48d8-b4fc-80d7f0b9c70e" class="com.becse.systemInfo" constructor="systemInfo()" target="systemCheck"/>
		<set-variable value="#[Java::invoke('com.becse.systemInfo', 'Info()', vars.systemCheck, {})]" doc:name="fetchSystemInfo" doc:id="ee071ae8-cfef-4137-9fad-143b8c98ccee" variableName="fetchSystemInfo" mimeType="application/java"/>
		<ee:transform doc:name="Transform Message" doc:id="a750f724-e384-438e-9911-d56ed4f5a922" >
			<ee:message >
				<ee:set-payload resource="statsUtilityMappings/prepare-system-info-request.dwl"/>
			</ee:message>
		</ee:transform>
		<set-payload value="#[payload]" doc:name="AssignSystemCheck" doc:id="19ae8692-1856-47bf-a112-88aaa65ff662" encoding="UTF-8" mimeType="application/json"/>
    
</flow>
	</mule>
