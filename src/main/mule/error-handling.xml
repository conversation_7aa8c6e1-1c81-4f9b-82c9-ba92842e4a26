<?xml version="1.0" encoding="UTF-8"?>

<mule xmlns:epdi-json-logger="http://www.mulesoft.org/schema/mule/epdi-json-logger"
	xmlns:api-log-generator="http://www.mulesoft.org/schema/mule/api-log-generator" xmlns:ee="http://www.mulesoft.org/schema/mule/ee/core"
	xmlns="http://www.mulesoft.org/schema/mule/core"
	xmlns:doc="http://www.mulesoft.org/schema/mule/documentation"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="
http://www.mulesoft.org/schema/mule/ee/core http://www.mulesoft.org/schema/mule/ee/core/current/mule-ee.xsd http://www.mulesoft.org/schema/mule/core http://www.mulesoft.org/schema/mule/core/current/mule.xsd
http://www.mulesoft.org/schema/mule/api-log-generator http://www.mulesoft.org/schema/mule/api-log-generator/current/mule-api-log-generator.xsd
http://www.mulesoft.org/schema/mule/epdi-json-logger http://www.mulesoft.org/schema/mule/epdi-json-logger/current/mule-epdi-json-logger.xsd">
	<error-handler name="global-error-handler"
		doc:id="a3a1fcef-647a-4b55-8f23-532e6cfb69a8">
		<!-- APIKit router related exceptions -->
		<on-error-propagate type="APIKIT:BAD_REQUEST"
			enableNotifications="true" logException="true" doc:name="On Error Propagate"
			doc:id="6ad02747-edb1-4307-bf89-928e409e32ad">
			<set-variable value="#[400]" doc:name="Set the HTTP Status - 400"
				doc:id="e129979a-6444-427f-b917-17deddbfd84c" variableName="httpStatus" />
			<flow-ref doc:name="global-prepare-error-response-sub-flow"
				doc:id="ef951fe8-521e-4c3a-b907-a77d766679b5" name="global-prepare-error-response-sub-flow" />
		</on-error-propagate>
		<on-error-propagate type="APIKIT:METHOD_NOT_ALLOWED"
			enableNotifications="true" logException="true" doc:name="On Error Propagate"
			doc:id="b63b14eb-b692-47df-bd87-1c0ac1f91ef9">
			<set-variable value="#[405]" doc:name="Set the HTTP Status - 405"
				doc:id="5e8b76e0-3a53-41fa-9b71-eeaf1149a49b" variableName="httpStatus" />
			<set-variable
				value="The method specified in the request is not allowed for this resource"
				doc:name="Set Error Message" doc:id="cdc40949-f040-4a5f-9f9a-277b377aa61e"
				variableName="errorMessage" />
			<flow-ref doc:name="global-prepare-error-response-sub-flow"
				doc:id="ef951fe8-521e-4c3a-b907-a77d766679b5" name="global-prepare-error-response-sub-flow" />
		</on-error-propagate>
		<on-error-propagate type="APIKIT:NOT_ACCEPTABLE"
			enableNotifications="true" logException="true" doc:name="On Error Propagate"
			doc:id="26c0ceb2-fb3f-44c9-92b0-095aca621ae2">
			<set-variable value="#[406]" doc:name="Set the HTTP Status - 406"
				doc:id="142e954e-cf87-4364-9dc7-ed124826d61e" variableName="httpStatus" />
			<set-variable
				value="The resource identified by the request is not capable of generating response entities according to the request accept headers"
				doc:name="Set Error Message" doc:id="e1766102-c2c6-4c87-aa9c-421910db47f3"
				variableName="errorMessage" />
			<flow-ref doc:name="global-prepare-error-response-sub-flow"
				doc:id="ef951fe8-521e-4c3a-b907-a77d766679b5" name="global-prepare-error-response-sub-flow" />
		</on-error-propagate>
		<on-error-propagate type="APIKIT:NOT_FOUND"
			enableNotifications="true" logException="true" doc:name="On Error Propagate"
			doc:id="9e54978c-18af-4db3-813a-93e0d49968db">
			<set-variable value="#[404]" doc:name="Set the HTTP Status - 404"
				doc:id="47caaba4-6e3c-45d6-bb00-75fee125a263" variableName="httpStatus" />
			<set-variable
				value="The server has not found anything matching the Request-URI"
				doc:name="Set Error Message" doc:id="9e6dd2e5-fa7d-4a6b-bd2b-243d4997896b"
				variableName="errorMessage" />
			<flow-ref doc:name="global-prepare-error-response-sub-flow"
				doc:id="ef951fe8-521e-4c3a-b907-a77d766679b5" name="global-prepare-error-response-sub-flow" />
		</on-error-propagate>
		<on-error-propagate type="APIKIT:UNSUPPORTED_MEDIA_TYPE"
			enableNotifications="true" logException="true" doc:name="On Error Propagate"
			doc:id="1f49c5b1-daef-449b-a56b-686508f10065">
			<set-variable value="#[415]" doc:name="Set the HTTP Status - 415"
				doc:id="423e535e-2227-4d6a-9037-c58ba526cf4d" variableName="httpStatus" />
			<set-variable
				value="The server is refusing to service the request because the entity of the request is in a format not supported by the requested resource for the requested method"
				doc:name="Set Error Message" doc:id="dd3b84e1-1625-4c4f-be7e-acbbda8e9625"
				variableName="errorMessage" />
			<flow-ref doc:name="global-prepare-error-response-sub-flow"
				doc:id="ef951fe8-521e-4c3a-b907-a77d766679b5" name="global-prepare-error-response-sub-flow" />
		</on-error-propagate>

		<!-- DB Related issues -->
		<on-error-propagate enableNotifications="true" logException="true" doc:name="On Error Propagate"
			doc:id="1f49c5b1-daef-449b-a56b-686508f11015" when="#[error.errorType.namespace == 'DB' and error.errorType.identifier == 'BAD_SQL_SYNTAX']">
			<set-variable value="#[500]" doc:name="Set the HTTP Status - 500"
				doc:id="f79eef8f-6008-4eed-8f5e-0604ed14ac5f" variableName="httpStatus" />
			<set-variable
				value="Bad SQL Syntax, please check the syntax of your sql query"
				doc:name="Set Error Message" doc:id="05e65ca8-70e3-4f8d-930d-733c3c01cbea"
				variableName="errorMessage" />
			<flow-ref doc:name="global-prepare-error-response-sub-flow"
				doc:id="ef951fe8-521e-4c3a-b907-a77d766679b5" name="global-prepare-error-response-sub-flow" />
		</on-error-propagate>
		<on-error-propagate enableNotifications="true" logException="true" doc:name="On Error Propagate"
			doc:id="1f49c5b1-daef-449b-a56b-686508f11016" when="#[error.errorType.namespace == 'DB' and error.errorType.identifier == 'CONNECTIVITY']">
			<set-variable value="#[503]" doc:name="Set the HTTP Status - 503"
				doc:id="0383b2f6-66e5-4ddc-9b0c-fa482776727d" variableName="httpStatus" />
			<set-variable value="Not able to connect to the Database"
				doc:name="Set Error Message" doc:id="744e8772-c7b4-47d6-b740-2107e9e7c76b"
				variableName="errorMessage" />
			<flow-ref doc:name="global-prepare-error-response-sub-flow"
				doc:id="ef951fe8-521e-4c3a-b907-a77d766679b5" name="global-prepare-error-response-sub-flow" />
		</on-error-propagate>
		<on-error-propagate enableNotifications="true" logException="true" doc:name="On Error Propagate"
			doc:id="1f49c5b1-daef-449b-a56b-686508f11017" when="#[error.errorType.namespace == 'DB' and error.errorType.identifier == 'QUERY_EXECUTION']">
			<set-variable value="#[500]" doc:name="Set the HTTP Status - 500"
				doc:id="b1065b64-757a-48af-a6f1-36a38df9a234" variableName="httpStatus" />
			<set-variable value="Error occured during Query Execution"
				doc:name="Set Error Message" doc:id="f709ee58-ac80-4dcf-a635-2ef2dfef884a"
				variableName="errorMessage" />
			<flow-ref doc:name="global-prepare-error-response-sub-flow"
				doc:id="ef951fe8-521e-4c3a-b907-a77d766679b5" name="global-prepare-error-response-sub-flow" />
		</on-error-propagate>
		<on-error-propagate enableNotifications="true" logException="true" doc:name="On Error Propagate"
			doc:id="1f49c5b1-daef-449b-a56b-686508f11018" when="#[error.errorType.namespace == 'DB' and error.errorType.identifier == 'RETRY_EXHAUSTED']">
			<set-variable value="#[503]" doc:name="Set the HTTP Status - 503"
				doc:id="36c17a80-b76a-43c0-a278-f313029ac998" variableName="httpStatus" />
			<set-variable value="DB Retries exhausted" doc:name="Set Error Message"
				doc:id="c9a1e6df-4832-45c0-9dd5-d0490453e6f5" variableName="errorMessage" />
			<flow-ref doc:name="global-prepare-error-response-sub-flow"
				doc:id="ef951fe8-521e-4c3a-b907-a77d766679b5" name="global-prepare-error-response-sub-flow" />
		</on-error-propagate>

		<!-- HTTP Requster Related error handling -->
		<on-error-propagate type="HTTP:BAD_REQUEST"
			enableNotifications="true" logException="true" doc:name="On Error Propagate"
			doc:id="1f49c5b1-daef-449b-a56b-686508f11000">
			<set-variable value="#[400]" doc:name="Set the HTTP Status - 400"
				doc:id="ee038ca2-d996-463d-b154-248e3eefd2d7" variableName="httpStatus" />
			<flow-ref doc:name="global-prepare-error-response-sub-flow"
				doc:id="ef951fe8-521e-4c3a-b907-a77d766679b5" name="global-prepare-error-response-sub-flow" />
		</on-error-propagate>
		<on-error-propagate type="HTTP:CLIENT_SECURITY"
			enableNotifications="true" logException="true" doc:name="On Error Propagate"
			doc:id="1f49c5b1-daef-449b-a56b-686508f11001">
			<set-variable value="#[401]" doc:name="Set the HTTP Status - 401"
				doc:id="0bc32029-17ce-455a-95aa-cdab83d6616d" variableName="httpStatus" />
			<set-variable value="Upstream service did not authorize the request."
				doc:name="Set Error Message" doc:id="98ed620a-a1aa-450b-9714-a4f7e78c384c"
				variableName="errorMessage" />
			<flow-ref doc:name="global-prepare-error-response-sub-flow"
				doc:id="ef951fe8-521e-4c3a-b907-a77d766679b5" name="global-prepare-error-response-sub-flow" />
		</on-error-propagate>
		<on-error-propagate type="HTTP:CONNECTIVITY"
			enableNotifications="true" logException="true" doc:name="On Error Propagate"
			doc:id="1f49c5b1-daef-449b-a56b-686508f11002">
			<set-variable value="#[503]" doc:name="Set the HTTP Status - 503"
				doc:id="97f2740c-e4ab-4971-a68d-8b1d92263915" variableName="httpStatus" />
			<flow-ref doc:name="global-prepare-error-response-sub-flow"
				doc:id="ef951fe8-521e-4c3a-b907-a77d766679b5" name="global-prepare-error-response-sub-flow" />
		</on-error-propagate>
		<on-error-propagate type="HTTP:FORBIDDEN"
			enableNotifications="true" logException="true" doc:name="On Error Propagate"
			doc:id="1f49c5b1-daef-449b-a56b-686508f11003">
			<set-variable value="#[403]" doc:name="Set the HTTP Status - 403"
				doc:id="381f8c4a-c79d-4f2f-b282-6781ba2ec113" variableName="httpStatus" />
			<set-variable value="Access to the upstream service is forbidden."
				doc:name="Set Error Message" doc:id="8286e4a5-97fd-4c4b-836f-a9f805f4dca8"
				variableName="errorMessage" />
			<flow-ref doc:name="global-prepare-error-response-sub-flow"
				doc:id="ef951fe8-521e-4c3a-b907-a77d766679b5" name="global-prepare-error-response-sub-flow" />
		</on-error-propagate>
		<on-error-propagate type="HTTP:INTERNAL_SERVER_ERROR"
			enableNotifications="true" logException="true" doc:name="On Error Propagate"
			doc:id="1f49c5b1-daef-449b-a56b-686508f11004">
			<set-variable value="#[500]" doc:name="Set the HTTP Status - 500"
				doc:id="03a8c236-2e77-4200-99d6-97c3bfcd424a" variableName="httpStatus" />
			<set-variable value="Upstream service unable to fulfil request."
				doc:name="Set Error Message" doc:id="fb0c52d2-a7a1-4831-a6b9-b25f3056e14b"
				variableName="errorMessage" />
			<flow-ref doc:name="global-prepare-error-response-sub-flow"
				doc:id="ef951fe8-521e-4c3a-b907-a77d766679b5" name="global-prepare-error-response-sub-flow" />
		</on-error-propagate>
		<on-error-propagate type="HTTP:METHOD_NOT_ALLOWED"
			enableNotifications="true" logException="true" doc:name="On Error Propagate"
			doc:id="1f49c5b1-daef-449b-a56b-686508f11005">
			<set-variable value="#[405]" doc:name="Set the HTTP Status - 405"
				doc:id="db6d3715-851d-4186-a971-e7674e1a901a" variableName="httpStatus" />
			<set-variable
				value="The method specified in the request is not allowed for this resource"
				doc:name="Set Error Message" doc:id="968e2b88-8216-465f-849e-deaf3de2f66d"
				variableName="errorMessage" />
			<flow-ref doc:name="global-prepare-error-response-sub-flow"
				doc:id="ef951fe8-521e-4c3a-b907-a77d766679b5" name="global-prepare-error-response-sub-flow" />
		</on-error-propagate>
		<on-error-propagate type="HTTP:NOT_ACCEPTABLE"
			enableNotifications="true" logException="true" doc:name="On Error Propagate"
			doc:id="1f49c5b1-daef-449b-a56b-686508f11006">
			<set-variable value="#[406]" doc:name="Set the HTTP Status - 406"
				doc:id="53b68ce2-4574-4176-90fe-4fc200cc2ac8" variableName="httpStatus" />
			<flow-ref doc:name="global-prepare-error-response-sub-flow"
				doc:id="ef951fe8-521e-4c3a-b907-a77d766679b5" name="global-prepare-error-response-sub-flow" />
		</on-error-propagate>
		<on-error-propagate type="HTTP:NOT_FOUND"
			enableNotifications="true" logException="true" doc:name="On Error Propagate"
			doc:id="1f49c5b1-daef-449b-a56b-686508f11007">
			<set-variable value="#[404]" doc:name="Set the HTTP Status - 404"
				doc:id="19a993c3-3783-4cec-b6db-2a317679f59b" variableName="httpStatus" />
			<set-variable
				value="The server has not found anything matching the Request-URI"
				doc:name="Set Error Message" doc:id="6c0f2701-f416-45ec-9c0d-f1d8c1bd4dcb"
				variableName="errorMessage" />
			<flow-ref doc:name="global-prepare-error-response-sub-flow"
				doc:id="ef951fe8-521e-4c3a-b907-a77d766679b5" name="global-prepare-error-response-sub-flow" />
		</on-error-propagate>
		<on-error-propagate type="HTTP:PARSING"
			enableNotifications="true" logException="true" doc:name="On Error Propagate"
			doc:id="1f49c5b1-daef-449b-a56b-686508f11008">
			<set-variable value="#[400]" doc:name="Set the HTTP Status - 400"
				doc:id="8c740de4-6d9b-4ac3-996a-2e26005d401b" variableName="httpStatus" />
			<flow-ref doc:name="global-prepare-error-response-sub-flow"
				doc:id="ef951fe8-521e-4c3a-b907-a77d766679b5" name="global-prepare-error-response-sub-flow" />
		</on-error-propagate>
		<on-error-propagate type="HTTP:RETRY_EXHAUSTED"
			enableNotifications="true" logException="true" doc:name="On Error Propagate"
			doc:id="1f49c5b1-daef-449b-a56b-686508f11009">
			<set-variable value="#[503]" doc:name="Set the HTTP Status - 503"
				doc:id="1598f932-90d3-4d9d-87ec-236c63306477" variableName="httpStatus" />
			<flow-ref doc:name="global-prepare-error-response-sub-flow"
				doc:id="ef951fe8-521e-4c3a-b907-a77d766679b5" name="global-prepare-error-response-sub-flow" />
		</on-error-propagate>
		<on-error-propagate type="HTTP:SECURITY"
			enableNotifications="true" logException="true" doc:name="On Error Propagate"
			doc:id="1f49c5b1-daef-449b-a56b-686508f11010">
			<set-variable value="#[401]" doc:name="Set the HTTP Status - 401"
				doc:id="4107181d-be74-4282-a3b2-913d4a3885bf" variableName="httpStatus" />
			<flow-ref doc:name="global-prepare-error-response-sub-flow"
				doc:id="ef951fe8-521e-4c3a-b907-a77d766679b5" name="global-prepare-error-response-sub-flow" />
		</on-error-propagate>
		<on-error-propagate type="HTTP:TIMEOUT"
			enableNotifications="true" logException="true" doc:name="On Error Propagate"
			doc:id="1f49c5b1-daef-449b-a56b-686508f11011">
			<set-variable value="#[504]" doc:name="Set the HTTP Status - 504"
				doc:id="faa94a2e-1e09-45f0-9a53-f6c79888ac42" variableName="httpStatus" />
			<flow-ref doc:name="global-prepare-error-response-sub-flow"
				doc:id="ef951fe8-521e-4c3a-b907-a77d766679b5" name="global-prepare-error-response-sub-flow" />
		</on-error-propagate>
		<on-error-propagate type="HTTP:TOO_MANY_REQUESTS"
			enableNotifications="true" logException="true" doc:name="On Error Propagate"
			doc:id="1f49c5b1-daef-449b-a56b-686508f11012">
			<set-variable value="#[429]" doc:name="Set the HTTP Status - 429"
				doc:id="9fa8ef3d-e3c9-4cd0-b749-6096c7413a9c" variableName="httpStatus" />
			<flow-ref doc:name="global-prepare-error-response-sub-flow"
				doc:id="ef951fe8-521e-4c3a-b907-a77d766679b5" name="global-prepare-error-response-sub-flow" />
		</on-error-propagate>
		<on-error-propagate type="HTTP:UNAUTHORIZED"
			enableNotifications="true" logException="true" doc:name="On Error Propagate"
			doc:id="1f49c5b1-daef-449b-a56b-686508f11013">
			<set-variable value="#[403]" doc:name="Set the HTTP Status - 403"
				doc:id="4948298f-04dc-4ed8-8006-5f6e57cc68ba" variableName="httpStatus" />
			<flow-ref doc:name="global-prepare-error-response-sub-flow"
				doc:id="ef951fe8-521e-4c3a-b907-a77d766679b5" name="global-prepare-error-response-sub-flow" />
		</on-error-propagate>
		<on-error-propagate type="HTTP:UNSUPPORTED_MEDIA_TYPE"
			enableNotifications="true" logException="true" doc:name="On Error Propagate"
			doc:id="1f49c5b1-daef-449b-a56b-686508f11014">
			<set-variable value="#[415]" doc:name="Set the HTTP Status - 415"
				doc:id="ddb2e945-1605-49b8-8867-6b23b062d473" variableName="httpStatus" />
			<flow-ref doc:name="global-prepare-error-response-sub-flow"
				doc:id="ef951fe8-521e-4c3a-b907-a77d766679b5" name="global-prepare-error-response-sub-flow" />
		</on-error-propagate>

		<!-- Streaming related exception -->
		<on-error-propagate type="STREAM_MAXIMUM_SIZE_EXCEEDED"
			enableNotifications="true" logException="true" doc:name="On Error Propagate"
			doc:id="660c144f-0c83-468d-9dfd-1e4dba2b1fa1">
			<set-variable value="#[500]" doc:name="Set the HTTP Status - 500"
				doc:id="7f1b3972-74b2-431b-a6d5-7643e8ae34bb" variableName="httpStatus" />
			<flow-ref doc:name="global-prepare-error-response-sub-flow"
				doc:id="ef951fe8-521e-4c3a-b907-a77d766679b5" name="global-prepare-error-response-sub-flow" />
		</on-error-propagate>

		<!-- Generic CONNECTIVITY Related Exception handling start. Order matters -->
		<on-error-propagate type="RETRY_EXHAUSTED"
			enableNotifications="true" logException="true" doc:name="On Error Propagate"
			doc:id="dd0bc428-ce88-46c8-83c7-c81d54d0352d">
			<set-variable value="#[503]" doc:name="Set the HTTP Status - 503"
				doc:id="789d5881-62ef-44bb-89d2-68ce1a986ebf" variableName="httpStatus" />
			<flow-ref doc:name="global-prepare-error-response-sub-flow"
				doc:id="ef951fe8-521e-4c3a-b907-a77d766679b5" name="global-prepare-error-response-sub-flow" />
		</on-error-propagate>
		<on-error-propagate type="REDELIVERY_EXHAUSTED"
			enableNotifications="true" logException="true" doc:name="On Error Propagate"
			doc:id="dd0bc428-ce88-46c8-83c7-c81d54d0352d">
			<set-variable value="#[503]" doc:name="Set the HTTP Status - 503"
				doc:id="296394f3-9160-4d3b-abba-79c70213aacb" variableName="httpStatus" />
			<flow-ref doc:name="global-prepare-error-response-sub-flow"
				doc:id="ef951fe8-521e-4c3a-b907-a77d766679b5" name="global-prepare-error-response-sub-flow" />
		</on-error-propagate>
		<on-error-propagate type="CONNECTIVITY"
			enableNotifications="true" logException="true" doc:name="On Error Propagate"
			doc:id="259cc9ce-cce1-4dd5-ae44-86b1fc7667fc">
			<set-variable value="#[503]" doc:name="Set the HTTP Status - 503"
				doc:id="b1f2717c-838b-45a8-8d91-4fc2b9bdd6af" variableName="httpStatus" />
			<flow-ref doc:name="global-prepare-error-response-sub-flow"
				doc:id="ef951fe8-521e-4c3a-b907-a77d766679b5" name="global-prepare-error-response-sub-flow" />
		</on-error-propagate>
		<on-error-propagate type="TIMEOUT"
			enableNotifications="true" logException="true" doc:name="On Error Propagate"
			doc:id="d1cf5b68-650c-4de2-aa26-e73ff556ab0e">
			<set-variable value="#[504]" doc:name="Set the HTTP Status - 504"
				doc:id="9b6fb4db-8089-480d-8ff4-cfe5d104c378" variableName="httpStatus" />
			<flow-ref doc:name="global-prepare-error-response-sub-flow"
				doc:id="ef951fe8-521e-4c3a-b907-a77d766679b5" name="global-prepare-error-response-sub-flow" />
		</on-error-propagate>
		<!-- Generic CONNECTIVITY Exception handling end -->

		<on-error-propagate type="TRANSFORMATION"
			enableNotifications="true" logException="true" doc:name="On Error Propagate"
			doc:id="a8f802c7-6231-4363-baca-c7d309de8a67">
			<set-variable value="#[400]" doc:name="Set the HTTP Status - 400"
				doc:id="bbdc6ce8-15c4-4953-bda1-5c484a695904" variableName="httpStatus" />
			<flow-ref doc:name="global-prepare-error-response-sub-flow"
				doc:id="ef951fe8-521e-4c3a-b907-a77d766679b5" name="global-prepare-error-response-sub-flow" />
		</on-error-propagate>

		<on-error-propagate enableNotifications="true"
			logException="true" doc:name="On Error Propagate" doc:id="e9791758-697c-44df-84e4-470dc99cd8de"
			when="#[error.errorType.namespace == 'SCRIPTING' and error.errorType.identifier == 'COMPILATION']">
			<set-variable value="#[500]" doc:name="Set the HTTP Status - 500"
				doc:id="8b1c2dab-023e-4855-bc22-c1be66113856" variableName="httpStatus" />
			<flow-ref doc:name="global-prepare-error-response-sub-flow"
				doc:id="1741213a-76c5-4fb0-95ca-022e3d5e1b68" name="global-prepare-error-response-sub-flow" />
		</on-error-propagate>
		<on-error-propagate enableNotifications="true"
			logException="true" doc:name="On Error Propagate" doc:id="50460440-80b7-41ea-9fb1-7c1ee60a766b"
			when="#[error.errorType.namespace == 'SCRIPTING' and error.errorType.identifier == 'EXECUTION']">
			<set-variable value="#[500]" doc:name="Set the HTTP Status - 500"
				doc:id="87ed4803-f9ba-4841-ab7f-ace3d5158b77" variableName="httpStatus" />
			<flow-ref doc:name="global-prepare-error-response-sub-flow"
				doc:id="51d8d45a-425d-4447-abbf-5279a5b79d0c" name="global-prepare-error-response-sub-flow" />
		</on-error-propagate>
		<on-error-propagate enableNotifications="true"
			logException="true" doc:name="On Error Propagate" doc:id="139aa836-274f-4b08-83c2-59f39ec3a106"
			when="#[error.errorType.namespace == 'SCRIPTING' and error.errorType.identifier == 'UNKNOWN_ENGINE']">
			<set-variable value="#[500]" doc:name="Set the HTTP Status - 500"
				doc:id="78029e55-92b6-4ea3-bc17-c3105cb9ec56" variableName="httpStatus" />
			<flow-ref doc:name="global-prepare-error-response-sub-flow"
				doc:id="67c46df0-bb29-434a-9e80-f6da33b854aa" name="global-prepare-error-response-sub-flow" />
		</on-error-propagate>

		<on-error-propagate type="EXPRESSION"
			enableNotifications="true" logException="true" doc:name="On Error Propagate"
			doc:id="8fe29b0a-d6e4-421f-b4fa-716c6e1f0744">
			<set-variable value="#[500]" doc:name="Set the HTTP Status - 500"
				doc:id="9926dee3-2b3c-451b-b374-5ecc518800df" variableName="httpStatus" />
			<flow-ref doc:name="global-prepare-error-response-sub-flow"
				doc:id="ef951fe8-521e-4c3a-b907-a77d766679b5" name="global-prepare-error-response-sub-flow" />
		</on-error-propagate>
		<on-error-propagate type="ROUTING"
			enableNotifications="true" logException="true" doc:name="On Error Propagate"
			doc:id="d1ac65da-c00f-478d-811c-91d4ce2ac640">
			<set-variable value="#[400]" doc:name="Set the HTTP Status - 400"
				doc:id="4058b6be-8b99-4153-907e-99d9f446480f" variableName="httpStatus" />
			<flow-ref doc:name="global-prepare-error-response-sub-flow"
				doc:id="ef951fe8-521e-4c3a-b907-a77d766679b5" name="global-prepare-error-response-sub-flow" />
		</on-error-propagate>
		<on-error-propagate type="SECURITY"
			enableNotifications="true" logException="true" doc:name="On Error Propagate"
			doc:id="6c33b909-ce81-40e6-bd41-b5383e085cbd">
			<set-variable value="#[401]" doc:name="Set the HTTP Status - 401"
				doc:id="7f933aaa-0f96-4e48-8c51-c1c6e9f9a601" variableName="httpStatus" />
			<flow-ref doc:name="global-prepare-error-response-sub-flow"
				doc:id="ef951fe8-521e-4c3a-b907-a77d766679b5" name="global-prepare-error-response-sub-flow" />
		</on-error-propagate>


		<!-- If none of the above matches then handle a the exception using generic 
			handler -->
		<on-error-propagate type="ANY" enableNotifications="true"
			logException="true" doc:name="On Error Propagate" doc:id="40180633-7687-4269-9ac4-b63bbe1ca580">
			<set-variable value="#[500]" doc:name="Set HTTP Status - 500"
				variableName="httpStatus" />
			<set-variable value="Upstream service unable to fulfil request."
				doc:name="Set Error Message" doc:id="43d37ef2-3170-4447-99df-15192a53feb1"
				variableName="errorMessage" />
			<flow-ref doc:name="global-prepare-error-response-sub-flow"
				doc:id="ef951fe8-521e-4c3a-b907-a77d766679b5" name="global-prepare-error-response-sub-flow" />
		</on-error-propagate>
		
	</error-handler>
	<sub-flow name="global-prepare-error-response-sub-flow"
		doc:id="7defbd45-9179-4e57-94c1-f35e3a830a4e">
		<ee:transform doc:name="Init Variables"
			doc:id="d4fae8db-5fd8-438a-b8d1-39072def6384">
			<ee:message>
			</ee:message>
			<ee:variables>
				<ee:set-variable variableName="errorRaised"><![CDATA[%dw 2.0
output application/java
---
true]]></ee:set-variable>
				<ee:set-variable variableName="errorMessage"><![CDATA[%dw 2.0
output application/java
---
if(vars.errorMessage?) 
	vars.errorMessage 
else 
	(vars.httpStatus ++ " : " ++ error.errorType.identifier)]]></ee:set-variable>
				<ee:set-variable variableName="errorDescription"><![CDATA[%dw 2.0
output application/java
---
if(vars.errorDescription?) 
	vars.errorDescription 
else 
	error.detailedDescription]]></ee:set-variable>
				<ee:set-variable variableName="logCategory"><![CDATA[%dw 2.0
output application/java
---
'Exception']]></ee:set-variable>
				<ee:set-variable variableName="logLevel"><![CDATA[%dw 2.0
output application/java
---
'ERROR']]></ee:set-variable>
			</ee:variables>
		</ee:transform>
		<ee:transform doc:name="Compose Error Payload"
			doc:id="65399b1e-e922-4457-b22f-9c8d7b3125f5">
			<ee:message>
				<ee:set-payload resource="errorHandlerMappings/global-error-handler.dwl" />
			</ee:message>
		</ee:transform>
		<async doc:name="logToELK" doc:id="5a2246ec-5fc0-40e5-986d-605c643cd4ba" >
			<epdi-json-logger:trace-it MCV="P4" doc:name="logToELK" doc:id="caa2d662-38b4-4e6c-9e40-9ba26a3095b7" OName="BECSE" DName="Retail" category="errorTrace" transactionStatusMessage="#[if (isEmpty(vars.errorIdentifier)) error.errorType.identifier else vars.errorIdentifier]" level="ERROR" transactionStatusCode="#[vars.httpStatus default 500]" />
		</async>
	</sub-flow>
</mule>
