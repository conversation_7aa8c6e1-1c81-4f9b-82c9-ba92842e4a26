<?xml version="1.0" encoding="UTF-8"?>

<mule xmlns:ee="http://www.mulesoft.org/schema/mule/ee/core"
	xmlns="http://www.mulesoft.org/schema/mule/core" xmlns:doc="http://www.mulesoft.org/schema/mule/documentation"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="
http://www.mulesoft.org/schema/mule/ee/core http://www.mulesoft.org/schema/mule/ee/core/current/mule-ee.xsd http://www.mulesoft.org/schema/mule/core http://www.mulesoft.org/schema/mule/core/current/mule.xsd">
	<sub-flow name="brand-to-osapiens-processingSub_Flow" doc:id="0d6e279d-f9ac-49b7-ad14-75a149ac3947">
	    <ee:transform doc:name="Parse payload" doc:id="39b2c922-1e4c-4484-829a-576bbc579c43">
			<ee:message>
					<ee:set-payload resource="requestMappings/parseKafkaMessages.dwl" />
			</ee:message>
		</ee:transform>
		<ee:transform doc:name="prepareRequestVars" doc:id="c171f70e-44c4-48e4-989e-4d27d76c7bec">
			<ee:message>
			</ee:message>
			<ee:variables>
				<ee:set-variable resource="requestMappings/requestToBP.dwl" variableName="httpRequest" />
			</ee:variables>
		</ee:transform>
			<flow-ref doc:name="system-api-requestSub_Flow" doc:id="91200e1d-6fad-48cd-9aa5-ee016adc99da" name="system-api-requestSub_Flow"/>
	</sub-flow>
	</mule>
