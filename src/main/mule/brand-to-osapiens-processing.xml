<?xml version="1.0" encoding="UTF-8"?>

<mule xmlns:ee="http://www.mulesoft.org/schema/mule/ee/core"
	xmlns="http://www.mulesoft.org/schema/mule/core" xmlns:doc="http://www.mulesoft.org/schema/mule/documentation"
	xmlns:epdi-json-logger="http://www.mulesoft.org/schema/mule/epdi-json-logger"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="
http://www.mulesoft.org/schema/mule/ee/core http://www.mulesoft.org/schema/mule/ee/core/current/mule-ee.xsd
http://www.mulesoft.org/schema/mule/core http://www.mulesoft.org/schema/mule/core/current/mule.xsd
http://www.mulesoft.org/schema/mule/epdi-json-logger http://www.mulesoft.org/schema/mule/epdi-json-logger/current/mule-epdi-json-logger.xsd">
	<sub-flow name="brand-to-osapiens-processingSub_Flow" doc:id="0d6e279d-f9ac-49b7-ad14-75a149ac3947">
		<set-variable value="#[payload]" doc:name="Store original Kafka batch" doc:id="store-original-kafka-batch" variableName="originalKafkaBatch"/>
	    <ee:transform doc:name="Parse payload" doc:id="39b2c922-1e4c-4484-829a-576bbc579c43">
			<ee:message>
					<ee:set-payload resource="requestMappings/parseKafkaMessages.dwl" />
			</ee:message>
		</ee:transform>

		<set-variable value="#[payload]" doc:name="Store original parsed message" doc:id="store-original-parsed-message" variableName="originalKafkaMessage"/>
		<choice doc:name="Is Parsed Payload Valid?" doc:id="parsed-payload-validation">
			<when expression="#[payload != null and not isEmpty(payload)]">
				<ee:transform doc:name="prepareRequestVars" doc:id="c171f70e-44c4-48e4-989e-4d27d76c7bec">
					<ee:message>
					</ee:message>
					<ee:variables>
						<ee:set-variable resource="requestMappings/requestToBP.dwl" variableName="httpRequest" />
					</ee:variables>
				</ee:transform>
				<epdi-json-logger:trace-it MCV="P4" DName="Retail" category="functionalTrace" doc:name="Trace it" doc:id="prepare-request-trace" OName="BECSE" correlationId="#[vars.trackingVar.'correlationId' default correlationId]" transactionStatusMessage="(Osapiens) After preparing HTTP request variables" payload='#[write({
					"httpRequest": vars.httpRequest,
					"payload": payload
				}, "application/json")]'/>
				<flow-ref doc:name="system-api-requestSub_Flow" doc:id="91200e1d-6fad-48cd-9aa5-ee016adc99da" name="system-api-requestSub_Flow"/>
			</when>
			<otherwise>
				<epdi-json-logger:trace-it MCV="P4" DName="Retail" category="functionalTrace" doc:name="Trace it" doc:id="invalid-parsed-payload-trace" OName="BECSE" correlationId="#[vars.trackingVar.'correlationId' default correlationId]" transactionStatusMessage="(Osapiens) Skipping sys API call - invalid parsed payload" payload='#[write({
					"parsedPayload": payload,
					"reason": if (payload == null) "null_parsed_payload" else "empty_parsed_payload"
				}, "application/json")]'/>
			</otherwise>
		</choice>
	</sub-flow>
	</mule>
