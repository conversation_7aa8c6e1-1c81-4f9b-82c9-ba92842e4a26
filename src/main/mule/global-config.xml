<?xml version="1.0" encoding="UTF-8"?>

<mule
	xmlns:tls="http://www.mulesoft.org/schema/mule/tls"
	xmlns:kafka="http://www.mulesoft.org/schema/mule/kafka"
	xmlns:keyvault-properties-provider="http://www.mulesoft.org/schema/mule/keyvault-properties-provider"
	xmlns:secure-properties="http://www.mulesoft.org/schema/mule/secure-properties"
	xmlns:api-gateway="http://www.mulesoft.org/schema/mule/api-gateway"
	xmlns:apikit="http://www.mulesoft.org/schema/mule/mule-apikit"
	xmlns:http="http://www.mulesoft.org/schema/mule/http"
	xmlns="http://www.mulesoft.org/schema/mule/core" xmlns:doc="http://www.mulesoft.org/schema/mule/documentation" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="
http://www.mulesoft.org/schema/mule/mule-apikit http://www.mulesoft.org/schema/mule/mule-apikit/current/mule-apikit.xsd 
http://www.mulesoft.org/schema/mule/http http://www.mulesoft.org/schema/mule/http/current/mule-http.xsd http://www.mulesoft.org/schema/mule/core http://www.mulesoft.org/schema/mule/core/current/mule.xsd
http://www.mulesoft.org/schema/mule/api-gateway http://www.mulesoft.org/schema/mule/api-gateway/current/mule-api-gateway.xsd
http://www.mulesoft.org/schema/mule/secure-properties http://www.mulesoft.org/schema/mule/secure-properties/current/mule-secure-properties.xsd
http://www.mulesoft.org/schema/mule/keyvault-properties-provider http://www.mulesoft.org/schema/mule/keyvault-properties-provider/current/mule-keyvault-properties-provider.xsd
http://www.mulesoft.org/schema/mule/kafka http://www.mulesoft.org/schema/mule/kafka/current/mule-kafka.xsd
http://www.mulesoft.org/schema/mule/tls http://www.mulesoft.org/schema/mule/tls/current/mule-tls.xsd">
	<http:listener-config name="httpListenerConfig"
		doc:name="HTTP Listener config"
		doc:id="f84a2d93-173b-4919-8b67-ea2c878fa861">
		<http:listener-connection
			host="${api.http.listener.host}" port="${api.http.listener.port}" />
	</http:listener-config>
	<apikit:config name="prc-partners-config"
		raml="prc-partners.raml"
		outboundHeadersMapName="outboundHeaders"
		httpStatusVarName="httpStatus"
		api="resource::b50fa1a0-96d4-45c4-819d-f472ae50bfa3:prc-partners:1.0.0:raml:zip:prc-partners.raml" queryParamsStrictValidation="true"/>
	<configuration-properties
		doc:name="Configuration properties"
		doc:id="e81772d0-c462-4acd-82d8-bd9a7af37588"
		file="${mule.env}.yaml" />
	<api-gateway:autodiscovery apiId="${api.id}"
		doc:name="API Autodiscovery"
		doc:id="c3debecc-f595-4265-8cb7-df1e3334c4a8"
		flowRef="prc-partners-api-main" />


	<http:request-config name="HTTP_Request_configuration_sys" doc:name="HTTP Request configuration" doc:id="e1ecf1ca-cbff-47dd-bcb5-a8899cc79a02" >
		<http:request-connection host='${app.host}' port='${app.port}' protocol="HTTPS">
            <tls:context >
				<tls:trust-store insecure="true" />
			</tls:context>
		</http:request-connection>
	</http:request-config>
    <keyvault-properties-provider:config name="Keyvault_Properties_Provider_Config" doc:name="Keyvault Properties Provider Config" doc:id="1e47eb21-0fcf-49b0-ae7c-ad54f1ce9309" applicationClientId="${vault.client.id}" applicationSecretKey="${vault.client.secret}" azureVaultName="${vault.name}" encryptKey="${vault.encrypt.key}" tenantId="a6b169f1-592b-4329-8f33-8db8903003c7"/>
    <global-property doc:name="Global Property" doc:id="29539987-df7e-49bf-855d-59b89724360e" name="mule.env" value="dev" />
    <global-property doc:name="Global Property" doc:id="53c6f353-79e9-4ad6-86cf-a8a2292fa34a" name="vault.name" value="dev-muleapps-keyvault" />
    <global-property doc:name="Global Property" doc:id="9d530b3a-20ac-4da6-9ba2-3711031ec97e" name="vault.client.id" value="1PvHCqmVzR47cFPYI1P3ipPP3lY3pWpHAQME7sG03wdSI1V6by+eIh67KNDdzFy/xZXmQ9MyuxQH/k0J8cyzCQ==" />
    <global-property doc:name="Global Property" doc:id="f4be80c2-ca1c-4c1b-aded-60c7319af36a" name="vault.client.secret" value="Eq/JkbUyvx9kQjzYMn1AD57GTcGEjGq9ULp6iO+ona/RR8FJKupoqIjyk5fx5t1pkB2JhfcymbM6dTI0KoaWcg==" />
    <global-property doc:name="Global Property" doc:id="dbc3928b-f990-4fcd-99f1-b700375bdc33" name="vault.encrypt.key" value="ITJyb2FjMkNXMmgqY3QlQ3Z2bk5EVm8xa1JYTiMwakI" />
	<kafka:consumer-config name="Apache_Kafka_Consumer_configuration" doc:name="Apache Kafka Consumer configuration" doc:id="ae52c10a-2c5c-4487-bc62-5e8202bc251b" >
		<kafka:consumer-sasl-plain-connection groupId="${kafka.consumer.group-id}" username="${kafka.consumer.api.key}" password="${kafka.consumer.api.secret}" endpointIdentificationAlgorithm="ldaps" tlsContext="Kafka_TLS_Context">
			<kafka:bootstrap-servers >
				<kafka:bootstrap-server value="${kafka.bootstrap.server}" />
			</kafka:bootstrap-servers>
            <kafka:additional-properties>
                <kafka:additional-property key="max.poll.records" value="100" />
                <kafka:additional-property key="fetch.max.bytes" value="524288" />
                <kafka:additional-property key="max.partition.fetch.bytes" value="262144" />
                <kafka:additional-property key="enable.auto.commit" value="false" />
                <kafka:additional-property key="session.timeout.ms" value="30000" />
                <kafka:additional-property key="heartbeat.interval.ms" value="3000" />
                <kafka:additional-property key="max.poll.interval.ms" value="300000" />
            </kafka:additional-properties>
			<kafka:topic-patterns >
				<kafka:topic-pattern value="${kafka.consumer.topic}" />
			</kafka:topic-patterns>
		</kafka:consumer-sasl-plain-connection>
	</kafka:consumer-config>

	<kafka:consumer-config name="Apache_Kafka_Retry_Consumer_configuration" doc:name="Apache Kafka Retry Consumer configuration" doc:id="retry-consumer-config">
		<kafka:consumer-sasl-plain-connection groupId="${kafka.consumer.retry.group-id}" username="${kafka.consumer.api.key}" password="${kafka.consumer.api.secret}" endpointIdentificationAlgorithm="ldaps" tlsContext="Kafka_TLS_Context">
			<kafka:bootstrap-servers>
				<kafka:bootstrap-server value="${kafka.bootstrap.server}" />
			</kafka:bootstrap-servers>
			<kafka:additional-properties>
				<kafka:additional-property key="max.poll.records" value="100" />
				<kafka:additional-property key="fetch.max.bytes" value="524288" />
				<kafka:additional-property key="max.partition.fetch.bytes" value="262144" />
				<kafka:additional-property key="enable.auto.commit" value="false" />
				<kafka:additional-property key="session.timeout.ms" value="30000" />
				<kafka:additional-property key="heartbeat.interval.ms" value="3000" />
				<kafka:additional-property key="max.poll.interval.ms" value="300000" />
			</kafka:additional-properties>
			<kafka:topic-patterns>
				<kafka:topic-pattern value="${kafka.consumer.retry.topic}" />
			</kafka:topic-patterns>
		</kafka:consumer-sasl-plain-connection>
	</kafka:consumer-config>

    <kafka:producer-config
    name="Apache_Kafka_Producer_configuration" 
    doc:name="Apache Kafka Producer configuration">
    <kafka:producer-sasl-plain-connection 
        username="${kafka.producer.api.key}" 
        password="${kafka.producer.api.secret}"
        idempotence="true" endpointIdentificationAlgorithm="ldaps" producerAck="ALL">
        <kafka:bootstrap-servers>
            <kafka:bootstrap-server value="${kafka.bootstrap.server}" />
        </kafka:bootstrap-servers>
        <kafka:additional-properties>
            <kafka:additional-property key="security.protocol" value="SASL_SSL" />
        </kafka:additional-properties>
    	</kafka:producer-sasl-plain-connection>
	</kafka:producer-config>
	<tls:context name="Kafka_TLS_Context" doc:name="TLS Context" doc:id="31994fd6-9648-4cbe-9164-0adff37624f2" >
 		<tls:trust-store path="cert/letsEncryptRoot.jks" password="123456"/>
	</tls:context>
</mule>
