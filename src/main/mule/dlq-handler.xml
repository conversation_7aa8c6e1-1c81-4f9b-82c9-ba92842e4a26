<?xml version="1.0" encoding="UTF-8"?>

<mule xmlns:kafka="http://www.mulesoft.org/schema/mule/kafka"
	xmlns:ee="http://www.mulesoft.org/schema/mule/ee/core"
	xmlns="http://www.mulesoft.org/schema/mule/core" 
	xmlns:doc="http://www.mulesoft.org/schema/mule/documentation"
	xmlns:epdi-json-logger="http://www.mulesoft.org/schema/mule/epdi-json-logger"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
	xsi:schemaLocation="
http://www.mulesoft.org/schema/mule/ee/core http://www.mulesoft.org/schema/mule/ee/core/current/mule-ee.xsd 
http://www.mulesoft.org/schema/mule/core http://www.mulesoft.org/schema/mule/core/current/mule.xsd
http://www.mulesoft.org/schema/mule/epdi-json-logger http://www.mulesoft.org/schema/mule/epdi-json-logger/current/mule-epdi-json-logger.xsd
http://www.mulesoft.org/schema/mule/kafka http://www.mulesoft.org/schema/mule/kafka/current/mule-kafka.xsd">

	<sub-flow name="dlq-handlerSub_Flow" doc:id="dlq-handler-sub-flow">
		<epdi-json-logger:trace-it MCV="P4" DName="Retail" category="functionalTrace" doc:name="Trace it" doc:id="dlq-handler-entry-trace" OName="BECSE" correlationId="#[vars.trackingVar.'correlationId' default correlationId]" transactionStatusMessage="(Osapiens) DLQ Handler - sending message to dead letter queue" payload='#[write({
			"originalMessage": vars.originalKafkaMessage,
			"downstreamResponse": payload,
			"dlqTopic": p('kafka.consumer.dlq.topic'),
			"reason": "downstream_system_error"
		}, "application/json")]'/>

		<ee:transform doc:name="Prepare DLQ Message" doc:id="prepare-dlq-message">
			<ee:message>
				<ee:set-payload><![CDATA[%dw 2.0
output application/json
---
{
	"originalMessage": vars.originalKafkaMessage,
	"downstreamResponse": payload,
	"errorDetails": {
		"statusCode": payload.statusCode,
		"errorCode": payload.errorCode,
		"message": payload.message,
		"referenceId": payload.referenceId,
		"requestItemIndex": payload.requestItemIndex
	},
	"processingMetadata": {
		"timestamp": now(),
		"correlationId": vars.trackingVar.'correlationId' default correlationId,
		"source": "prc-partners",
		"dlqReason": "downstream_system_error"
	}
}]]></ee:set-payload>
			</ee:message>
		</ee:transform>
		
		<try doc:name="Try DLQ Publish" doc:id="try-dlq-publish">
            <kafka:publish doc:name="publishToKafkaDlqTopic" doc:id="2bb60011-468e-47f4-a68e-241d1bae2149" config-ref="Apache_Kafka_Producer_configuration" topic="${kafka.consumer.dlq.topic}" key="#[vars.originalKafkaMessage.businessPartnerId as String]"/>
			<epdi-json-logger:trace-it MCV="P4" DName="Retail" category="functionalTrace" doc:name="Trace it" doc:id="dlq-publish-success-trace" OName="BECSE" correlationId="#[vars.trackingVar.'correlationId' default correlationId]" transactionStatusMessage="(Osapiens) DLQ Handler - message successfully sent to dead letter queue"/>
			<error-handler>
				<on-error-continue enableNotifications="true" logException="true" doc:name="Handle DLQ publish errors" doc:id="dlq-publish-error-handler">
					<epdi-json-logger:trace-it MCV="P4" DName="Retail" category="errorTrace" doc:name="Trace it" doc:id="dlq-publish-error-trace" OName="BECSE" correlationId="#[vars.trackingVar.'correlationId' default correlationId]" transactionStatusMessage="(Osapiens) DLQ Handler - FAILED to send message to dead letter queue" payload='#[write({
						"error": {
							"type": error.errorType,
							"description": error.description,
							"detailedDescription": error.detailedDescription,
							"cause": error.cause,
							"childErrors": error.childErrors,
							"errorMessage": error.errorMessage default "No error message available"
						},
						"kafkaConfiguration": {
							"dlqTopic": p('kafka.consumer.dlq.topic'),
							"bootstrapServer": p('kafka.bootstrap.server'),
							"producerApiKey": if (p('kafka.producer.api.key') != null) "***CONFIGURED***" else "NOT_CONFIGURED"
						},
						"messageDetails": {
							"originalMessage": vars.originalKafkaMessage,
							"messageKey": vars.originalKafkaMessage.businessPartnerId default "unknown",
							"messageSize": sizeOf(payload default {}),
							"correlationId": vars.trackingVar.'correlationId' default correlationId
						}
					}, "application/json")]'/>
				</on-error-continue>
			</error-handler>
		</try>
	</sub-flow>
</mule>
