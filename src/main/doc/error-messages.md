#### Different Error Messages returned from the prc-partners

Possible status codes:

| Status        | Error Code           | Description |
| ------------- |:-------------| :-----|
| 400 | BAD_REQUEST | The request could not be understood by the server due to malformed syntax. The client SHOULD NOT repeat the request without modifications. |
| 404 | RESOURCE_NOT_FOUND | The server has not found anything matching the request URI. |
| 405 | METHOD_NOT_ALLOWED | The method specified in the request is not allowed for the resource identified by the URI. |
| 406 | NOT_ACCEPTABLE | The request contains parameters that are not acceptable. |
| 409 | CONFLICT | The request could not be completed due to a conflict with the current state of the resource. This code is only allowed in situations where it is expected that the user might be able to resolve the conflict and resubmit the request. |
| 415 | UNSUPPORTED_MEDIA_TYPE | The server is refusing to service the request because the entity of the request is in a format not supported by the requested resource for the requested method. |
| 500 | INTERNAL_SERVER_ERROR | The server encountered an unexpected condition which prevented it from fulfilling the request. |
| 502 | BAD_GATEWAY | The server, while acting as a gateway or proxy, received an invalid response from the upstream server it accessed in attempting to fulfill the request. |


Example message body:

```
{
    "error": {
        "errorCode": "RESOURCE_NOT_FOUND",
        "errorDateTime": "2018-01-08T11:39:55+0100",
        "errorMessage": "Upstream service internal error.",
        "errorDescription": "The server has not found anything matching the request URI"
    }
}
```