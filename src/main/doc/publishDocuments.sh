#!/bin/bash
###########################################################################
#Description: This is a script to update the documents every time a deployment is made 
#             This will update the documents kept in src/main/doc folder to exchange.
#Author: API Team
###########################################################################
anypoint-cli-v4 exchange asset page upload b50fa1a0-96d4-45c4-819d-f472ae50bfa3/prc-partners/1.0.0 home main-page.md
anypoint-cli-v4 exchange asset page upload b50fa1a0-96d4-45c4-819d-f472ae50bfa3/prc-partners/1.0.0 "Getting Started" getting-started.md
anypoint-cli-v4 exchange asset page upload b50fa1a0-96d4-45c4-819d-f472ae50bfa3/prc-partners/1.0.0 "Error Messages" error-messages.md
anypoint-cli-v4 exchange asset page upload b50fa1a0-96d4-45c4-819d-f472ae50bfa3/prc-partners/1.0.0 "Security" security.md
anypoint-cli-v4 exchange asset page upload b50fa1a0-96d4-45c4-819d-f472ae50bfa3/prc-partners/1.0.0 "Release Notes" release-notes.md
anypoint-cli-v4 exchange asset page upload b50fa1a0-96d4-45c4-819d-f472ae50bfa3/prc-partners/1.0.0 Audience audience.md
anypoint-cli-v4 exchange asset page upload b50fa1a0-96d4-45c4-819d-f472ae50bfa3/prc-partners/1.0.0 "Contact Us" contact.md
anypoint-cli-v4 exchange asset page upload b50fa1a0-96d4-45c4-819d-f472ae50bfa3/prc-partners/1.0.0 "SLAs" support.md
