To access the prc-partners, you essentially need three things: The URL, the client-id and a client-secret:

```
{
  "baseUrl": "https://someurl/v1",
  "clientId": "somefakeclientid",
  "clientSecret": "somefakeclientsecret",
}
```

The client-id and client-secret are passed as HTTP request headers.

The client-id and client-secret are usually created per consuming application and created by the API owner. Currently that's a bit tricky because oftentimes, actual API domain-owners don't have access to Anypoint Exchange. The idea is that that will change over time, but currently, if you require access to an API, it's best to email the Creator of the API who can either arrange access directly or point you in the right direction.


### Example Usage
Since you're reading this to actually get started, let's take a look at a few example usages of the API:

#### Get a specific personal offer by offerid:
```
GET /v1/<<endpoint>>/1026
```

##### Response:
```
{
"storeid": 1026,
"date": [
  "2018-07-18",
  "2018-07-18",
  "2018-07-18"
],
}
```

### Postman Collection

The following [Postman collection file][2] contains sample requests for the api end points.


  [1]: <URL OF THE POSTMAN COLLECTION CHECKED IN TO BITBUCKET>