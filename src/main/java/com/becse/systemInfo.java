package com.becse;

import java.io.File;
import java.text.NumberFormat;
import java.util.LinkedHashMap;


public class systemInfo {
	private Runtime runtime = Runtime.getRuntime();

	public systemInfo() {
		
	}
    public LinkedHashMap<Object, Object> Info() {
                LinkedHashMap<Object, Object> info = new LinkedHashMap<Object, Object>();     
        info.put("OSInfo",this.OsInfo());
        info.put("MemInfo",this.MemInfo());
        info.put("DiskInfo",this.DiskInfo());
        return info;
    }

    public String OSname() {
        return System.getProperty("os.name");
    }

    public String OSversion() {
        return System.getProperty("os.version");
    }

    public String OsArch() {
        return System.getProperty("os.arch");
    }

    public long totalMem() {
        return Runtime.getRuntime().totalMemory();
    }

    public long usedMem() {
        return Runtime.getRuntime().totalMemory() - Runtime.getRuntime().freeMemory();
    }

    public LinkedHashMap<Object, Object> MemInfo() {
        NumberFormat format = NumberFormat.getInstance();
        LinkedHashMap<Object, Object> meminfo = new LinkedHashMap<Object, Object>();
        long maxMemory = runtime.maxMemory();
        long allocatedMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        meminfo.put("FreeMemory", (format.format(freeMemory / 1024)+" kb") );
        meminfo.put("AllocatedMemory", (format.format(allocatedMemory / 1024)+ " kb"));
        meminfo.put("MaxMemory", (format.format(maxMemory / 1024)+ " kb"));
        meminfo.put("TotalFreeMemory", (format.format((freeMemory + (maxMemory - allocatedMemory)) / 1024)+ " kb"));
        return meminfo;
    }

    public LinkedHashMap<Object, Object> OsInfo() {
                LinkedHashMap<Object, Object> osinfo = new LinkedHashMap<Object, Object>();
                osinfo.put("OperatingSystem",this.OSname());
                osinfo.put("OSVersion",this.OSversion());
                osinfo.put("OSArchitecture",this.OsArch());
                osinfo.put("AvailableProcessors(cores)",runtime.availableProcessors());
                                return osinfo;
    }

    public LinkedHashMap<Object, Object> DiskInfo() {
        /* Get a list of all filesystem roots on this system */
        File[] roots = File.listRoots();
        LinkedHashMap<Object, Object> diskinfo = new LinkedHashMap<Object, Object>();
          int count=1;
        /* For each filesystem root, print some info */
        for (File root : roots) {
               diskinfo.put("FileSystemRoot-"+count,root.getAbsolutePath());
               diskinfo.put("TotalSpace-"+count,root.getTotalSpace() + " bytes");
               diskinfo.put("FreeSpace-"+count,root.getFreeSpace() + " bytes");
               diskinfo.put("UsableSpace-"+count,root.getUsableSpace() + " bytes");
                                                count=count+1;
        } 
        return diskinfo;
    }


}
