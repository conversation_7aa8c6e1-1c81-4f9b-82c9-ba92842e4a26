%dw 2.0
output application/json
---
[
  {
  "application": payload."0".payload.application,
      "status": payload."0".payload.status,
      "remarks": payload."0".payload.remarks,
      "dateTime": now() as DateTime {format: "yyyy-MM-dd'T'HH:mm:ss'Z'"}
  },{
    "application": payload."1".payload.application,
      "status": payload."1".payload.status,
      "remarks": payload."1".payload.remarks,
      "dateTime": now() as DateTime {format: "yyyy-MM-dd'T'HH:mm:ss'Z'"}}]
