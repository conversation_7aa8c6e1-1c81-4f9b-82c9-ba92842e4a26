%dw 2.0
output application/json
---
{
  "application": vars.appName,
  "status": (if (vars.Status == 200 or vars.Status == 400 or vars.Status == 405 or vars.Status == 406 or vars.Status == 'OK') p('app.ok') else p('app.nok')),
  "remarks": if (vars.Status == 200 or vars.Status == 400 or vars.Status == 405 or vars.Status == 406 or vars.Status == 'OK') (vars.appName ++ " is alive and reachable") else (vars.appName ++ " is not reachable and the reason of the failure is " ++ (vars.setReason default "ConnectionException"))
}