# Application Configuration
application:
  name: "prc-partners"

#API Configurations
api:
  name: "groupId:b50fa1a0-96d4-45c4-819d-f472ae50bfa3.becse:assetId:prc-partners"
  version: "v1:20490736"
  id: "20490736"
  http:
    listener:
      path: "/v1/*"
      console: "/v1/console/*"
      host: "0.0.0.0"
      port: "8081"

#Proxy Configurations
proxy:
  host: "proxy.aholdusa.com"
  port: "8080"
  
#App Stats
app:
  ok: "OK (Working)"
  nok: "NOK (Not Working)"
  host: ${fqdn_env}
  port: "443"

# Kafka Configuration
kafka:  # Polling config to limit batch size and prevent heap issues
  # Bootstrap Server Configuration
  bootstrap:
    server: "pkc-57q33g.westeurope.azure.confluent.cloud:9092"

  producer:
    api:
      key: ${azure-vault::becse-retail-prc-suppliers-producer-api-key}
      secret: ${azure-vault::becse-retail-prc-suppliers-producer-api-secret}

  # Consumer Configuration
  consumer:
    group-id: "consumer-prc-partners"
    topic: "retail.osapiens.supplier.main.v1"
    dlq:
      topic: "retail.osapiens.supplier.dlq.v1"
    isolation-level: "READ_COMMITTED"
    api:
      key: ${azure-vault::becse-retail-prc-partners-kafka-consumer-api-key}
      secret: ${azure-vault::becse-retail-prc-partners-kafka-consumer-api-secret}

#sys-api 
system:
  bp:
    path: "/sys-osapiens-hub/v1/business-partners"