%dw 2.0
import * from dw::util::Timer
output application/json
---
{
	"startTimer": currentMilliseconds() as Number,
	"httpMethod": attributes.method,
	"invokedPath": attributes.maskedRequestPath default "NON_HTTP_REQUEST",
	"transactionId": attributes.headers.'x-transaction-id' default correlationId,
	"correlationId": attributes.headers.'correlationId' default correlationId,
	"clientMachineAddress": (if ( !(isEmpty(attributes.headers.'X-Forwarded-For')) ) attributes.headers.'X-Forwarded-For' else if ( !(isEmpty(attributes.headers.'x-forwarded-for')) ) attributes.headers.'x-forwarded-for' else if ( !(isEmpty(attributes.remoteAddress)) ) attributes.remoteAddress else "0.0.0.0"),
}

