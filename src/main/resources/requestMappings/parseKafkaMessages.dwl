%dw 2.0
output application/json

fun safeJsonParse(value, defaultOutput = null) =
    dw::Runtime::try(() ->
        if (value is String and trim(value) startsWith "{" and trim(value) endsWith "}")
            read(value, "application/json")
        else
            value
    ) match {
        case tr if (tr.success) -> tr.result
        else -> defaultOutput  // Return null if parsing fails, so it gets filtered out
    }

fun isValidPayload(value) =
    value != null and
    value != "" and
    not isEmpty(value)

fun extractKafkaPayload(kafkaMessage) = do {
    var messagePayload = 
        if (kafkaMessage.serByteArray.inputStream != null) 
            kafkaMessage.serByteArray.inputStream
        else 
            kafkaMessage.payload
    
    var cleanedPayload = if (messagePayload is String and messagePayload != null and messagePayload != "")
        trim(messagePayload)
    else
        messagePayload
    
    // More aggressive JSON parsing - ensure we get objects, not strings
    var parsedPayload = if (cleanedPayload is String and cleanedPayload != null and cleanedPayload != "")
        safeJsonParse(cleanedPayload)
    else if (cleanedPayload != null)
        cleanedPayload
    else
        null
    
    var finalPayload = if (parsedPayload is String and trim(parsedPayload) startsWith "{")
        safeJsonParse(parsedPayload)
    else
        parsedPayload
    ---
    if (isValidPayload(finalPayload)) finalPayload else null
}

var kafkaMessages = payload default []
var extractedPayloads = kafkaMessages map extractKafkaPayload($)
var validMessages = extractedPayloads filter ($ != null)

---
validMessages
