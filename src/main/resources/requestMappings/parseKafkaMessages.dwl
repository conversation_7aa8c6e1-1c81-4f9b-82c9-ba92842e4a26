%dw 2.0
output application/json
fun defaultInvalidEvents (fn, defaultOutput = null) = dw::Runtime::try(fn) match {
    case tr if (tr.success) -> tr.result
    else -> defaultOutput
}
var eventAttributes = payload.*attributes default [] map (value) -> {
    attributes: {
        creationTimestamp: value.creationTimestamp,
        partition: value.partition,
        offset: value.offset,
        transactionId: value.headers.transactionId
    }
}
var eventValues = (payload.*payload default [] map (value, index) -> do {
    var validJson = defaultInvalidEvents(() -> read(value, "application/json"))
    ---
    value: if (validJson is Null) null else validJson
})
var processedMessages = ((eventAttributes zip eventValues) map (value) -> {
    attributes: value.attributes[0],
    value: value.value[0]
}) filter ($.value != null)
---
if (sizeOf(processedMessages) > 0) 
    processedMessages[0].value 
else 
    null
