%dw 2.0
output application/json
fun safeJsonParse(value, defaultOutput = null) =
    dw::Runtime::try(() -> read(value, "application/json")) match {
        case tr if (tr.success) -> tr.result
        else -> defaultOutput
    }

fun isValidPayload(value) =
    value != null and
    value != "" and
    not isEmpty(value)

fun extractKafkaPayload(kafkaMessage) = do {
    var messagePayload = kafkaMessage.payload
    var parsedPayload = if (messagePayload is String)
        safeJsonParse(messagePayload)
    else
        messagePayload
    ---
    if (isValidPayload(parsedPayload)) parsedPayload else null
}

var kafkaMessages = payload default []
var validMessages = kafkaMessages
    map extractKafkaPayload($)
    filter ($ != null)

---
if (sizeOf(validMessages) > 0)
    validMessages[0]
else
    null
