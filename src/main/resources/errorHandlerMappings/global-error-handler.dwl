%dw 2.0
output application/json encoding="UTF-8", skipNullOn="everywhere"
---
error: {
	errorCode: (vars.httpStatus ++ " : " ++ error.errorType.identifier),
	errorMessage: if(vars.errorMessage != null) vars.errorMessage else (error.errorType.identifier),
	errorDescription: if(error.errorType.identifier == "EXPRESSION") "MuleSoft parser was not able to parse the request, it can be caused due to invalid body or parameters passed in the request. Kindly check the body passed as request or contact the MuleSoft team to help." else if(vars.errorDescription != null) vars.errorDescription else error.description,
	errorDateTime: now() as String { format: "yyyy-MM-dd'T'HH:mm:ss" }
}