<?xml version="1.0" encoding="UTF-8"?>
<classpath>
	<classpathentry kind="con" path="MULE_LIB/com.mulesoft.connectors/mule-kafka-connector/4.10.2"/>
	<classpathentry kind="con" path="MULE_LIB/org.mule.module/mule-java-module/1.2.13"/>
	<classpathentry kind="con" path="MULE_LIB/com.mulesoft.munit/munit-tools/3.4.0"/>
	<classpathentry kind="con" path="MULE_LIB/com.mulesoft.munit/munit-runner/3.4.0"/>
	<classpathentry kind="con" path="MULE_LIB/b144f004-790a-4e9b-8ff3-61a28db48356/extension-rgco-acpf-keyvault-provider/1.2.0"/>
	<classpathentry kind="con" path="org.mule.tooling.API_SPEC_LIB/b50fa1a0-96d4-45c4-819d-f472ae50bfa3/weather-forecast-api/1.0.0"/>
	<classpathentry kind="con" path="MULE_LIB/org.mule.modules/mule-apikit-module/1.11.5"/>
	<classpathentry kind="con" path="MULE_LIB/org.mule.connectors/mule-http-connector/1.10.3"/>
	<classpathentry kind="con" path="MULE_LIB/b50fa1a0-96d4-45c4-819d-f472ae50bfa3/epdi-json-logger/1.0.0"/>
	<classpathentry kind="src" path="src/main/mule"/>
	<classpathentry kind="src" output="target/classes" path="src/main/java">
		<attributes>
			<attribute name="optional" value="true"/>
			<attribute name="maven.pomderived" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry excluding="**" kind="src" output="target/classes" path="src/main/resources">
		<attributes>
			<attribute name="maven.pomderived" value="true"/>
			<attribute name="optional" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="src" output="target/test-classes" path="src/test/java">
		<attributes>
			<attribute name="optional" value="true"/>
			<attribute name="maven.pomderived" value="true"/>
			<attribute name="test" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry excluding="**" kind="src" output="target/test-classes" path="src/test/resources">
		<attributes>
			<attribute name="maven.pomderived" value="true"/>
			<attribute name="test" value="true"/>
			<attribute name="optional" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="con" path="org.eclipse.jdt.launching.JRE_CONTAINER/org.eclipse.jdt.internal.debug.ui.launcher.StandardVMType/JavaSE-17">
		<attributes>
			<attribute name="maven.pomderived" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="con" path="org.eclipse.m2e.MAVEN2_CLASSPATH_CONTAINER">
		<attributes>
			<attribute name="maven.pomderived" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="src" path="target/generated-sources/annotations">
		<attributes>
			<attribute name="optional" value="true"/>
			<attribute name="maven.pomderived" value="true"/>
			<attribute name="ignore_optional_problems" value="true"/>
			<attribute name="m2e-apt" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry exported="true" kind="con" path="MULE_RUNTIME/org.mule.tooling.server.4.9.ee"/>
	<classpathentry kind="src" output="target/test-classes" path="target/generated-test-sources/test-annotations">
		<attributes>
			<attribute name="optional" value="true"/>
			<attribute name="maven.pomderived" value="true"/>
			<attribute name="ignore_optional_problems" value="true"/>
			<attribute name="m2e-apt" value="true"/>
			<attribute name="test" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="output" path="target/classes"/>
</classpath>
