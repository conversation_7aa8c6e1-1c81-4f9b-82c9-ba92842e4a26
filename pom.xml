<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<groupId>b50fa1a0-96d4-45c4-819d-f472ae50bfa3</groupId>
	<artifactId>prc-partners-app</artifactId>
	<version>1.0.0-${build_id}</version>
	<packaging>mule-application</packaging>
	<name>prc-partners</name>
	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
		<application.version>v1</application.version>

		<mule.maven.plugin.version>4.3.0</mule.maven.plugin.version>
		<mule.maven.clean.version>3.2.0</mule.maven.clean.version>
		<mule.maven.munit.version>3.4.0</mule.maven.munit.version>
		<munit.version>3.4.0</munit.version>
		<munit.runner.version>3.4.0</munit.runner.version>
		<munit.tools.version>3.4.0</munit.tools.version>

		<app.runtime>4.9.4</app.runtime>

		<maven.compiler.source>17</maven.compiler.source>
		<maven.compiler.target>17</maven.compiler.target>
		<!-- JDK version -->
		<jdk.version>17</jdk.version>
		<mule.provider>MC</mule.provider>
		<mule.applicationName>prc-partners</mule.applicationName>
		<mule.businessGroup>Koninklijke Ahold Delhaize N.V.\BE-CSE\BECSE RETAIL</mule.businessGroup>
		<mule.environment>${mule_env}</mule.environment>
		<mule.app.url>https://${fqdn_env}/prc-partners${rewrite}</mule.app.url>
		<!-- Faster GitHub action => Check RTF for progress -->
		<skip.deployment.verification>true</skip.deployment.verification>
		<log.wricef.logPayload>false</log.wricef.logPayload>

		<categories>[]</categories>
		<tags>prc-partners,becse</tags>
		<maven.dependency.plugin.version>3.1.1</maven.dependency.plugin.version>
		<maven.compiler.plugin.version>3.8.1</maven.compiler.plugin.version>
		<maven.remote.resources.plugin.version>3.0.0</maven.remote.resources.plugin.version>
		<extension.rgco.acpf.keyvault.provider.version>1.2.0</extension.rgco.acpf.keyvault.provider.version>
	</properties>
	<build>
		<resources>
			<resource>
				<directory>${basedir}/src/main/resources</directory>
				<filtering>false</filtering>
			</resource>
			<resource>
				<directory>${basedir}/src/main/resources</directory>
				<filtering>true</filtering>
				<includes>
					<include>dev.yaml</include>
					<include>qa.yaml</include>
					<include>acc.yaml</include>
					<include>prd.yaml</include>
				</includes>
			</resource>
		</resources>
		<!-- end -->
		<plugins>
			<plugin>
				<groupId>org.mule.tools.maven</groupId>
				<artifactId>mule-maven-plugin</artifactId>
				<version>${mule.maven.plugin.version}</version>
				<extensions>true</extensions>
				<configuration>
					<runtimeFabricDeployment>
						<muleVersion>4.9.4:4-java17</muleVersion>
						<connectedAppClientId>${param_app_client}</connectedAppClientId>
						<connectedAppClientSecret>${param_app_secret}</connectedAppClientSecret>
						<connectedAppGrantType>client_credentials</connectedAppGrantType>

						<applicationName>${mule.applicationName}</applicationName>
						<businessGroup>${mule.businessGroup}</businessGroup>
						<releaseChannel>LTS</releaseChannel>
						<environment>${mule.environment}</environment>
						<replicas>${env_replicas}</replicas>
						<provider>${mule.provider}</provider>
						<target>${rtf-name}</target>
						<uri>https://anypoint.mulesoft.com</uri>
						<skipDeploymentVerification>${skip.deployment.verification}</skipDeploymentVerification>
						<properties>
							<anypoint.platform.client_id>${client_id}</anypoint.platform.client_id>
							<anypoint.platform.client_secret>${client_secret}</anypoint.platform.client_secret>
							<mule.env>${param_env}</mule.env>
							<vault.name>${vault_name}</vault.name>
							<vault.client.id>${vault_client_id}</vault.client.id>
							<vault.client.secret>${vault_client_secret}</vault.client.secret>
							<vault.encrypt.key>${vault_encrypt_key}</vault.encrypt.key>
							<logging.level.org.mule.extension.jsonlogger.JsonLogger>INFO</logging.level.org.mule.extension.jsonlogger.JsonLogger>
							<log.wricef.logPayload>${log.wricef.logPayload}</log.wricef.logPayload>
							<fqdn_env>${fqdn_env}</fqdn_env>
							<json_logger_pretty_print>${json_logger_pretty_print}</json_logger_pretty_print>
						</properties>
						<deploymentSettings>
							<updateStrategy>rolling</updateStrategy>
							<resources>
								<cpu>
									<reserved>${mule_app_cpu}</reserved>
									<limit>${mule_app_cpu_max}</limit>
								</cpu>
								<memory>
									<reserved>${mule_app_mem}</reserved>
								</memory>
							</resources>
							<http>
								<inbound>
									<publicUrl>${mule.app.url}</publicUrl>
								</inbound>
							</http>
						</deploymentSettings>
					</runtimeFabricDeployment>
				</configuration>
			</plugin>
			<!-- for using java parameters by name instead of arg0, arg1... -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>${maven.compiler.plugin.version}</version>
				<configuration>
					<parameters>true</parameters>
					<source>${jdk.version}</source>
					<target>${jdk.version}</target>
					<compilerArgs>
						<args>-parameters</args>
					</compilerArgs>
				</configuration>
			</plugin>
			<!-- MUnit -->
			<plugin>
				<groupId>com.mulesoft.munit.tools</groupId>
				<artifactId>munit-maven-plugin</artifactId>
				<version>${munit.version}</version>
				<executions>
					<execution>
						<id>test</id>
						<phase>test</phase>
						<goals>
							<goal>test</goal>
							<goal>coverage-report</goal>
						</goals>
					</execution>
				</executions>
				<configuration>
					<skipMunitTests>true</skipMunitTests>
					<coverage>
						<runCoverage>true</runCoverage>
						<formats>
							<format>html</format>
						</formats>
						<ignoreFiles>
							<ignoreFile>globals.xml</ignoreFile>
						</ignoreFiles>
					</coverage>
				</configuration>
			</plugin>
		</plugins>
	</build>

	<dependencies>
		<dependency>
			<groupId>b50fa1a0-96d4-45c4-819d-f472ae50bfa3</groupId>
			<artifactId>epdi-json-logger</artifactId>
			<version>1.0.0</version>
			<classifier>mule-plugin</classifier>
		</dependency>
		<dependency>
			<groupId>b144f004-790a-4e9b-8ff3-61a28db48356</groupId>
			<artifactId>extension-rgco-acpf-keyvault-provider</artifactId>
			<version>${extension.rgco.acpf.keyvault.provider.version}</version>
			<classifier>mule-plugin</classifier>
		</dependency>
		<dependency>
			<groupId>b50fa1a0-96d4-45c4-819d-f472ae50bfa3</groupId>
			<artifactId>prc-partners</artifactId>
			<version>1.0.0</version>
			<classifier>raml</classifier>
			<type>zip</type>
		</dependency>
		<dependency>
			<groupId>org.mule.connectors</groupId>
			<artifactId>mule-http-connector</artifactId>
			<version>1.10.3</version>
			<classifier>mule-plugin</classifier>
		</dependency>
		<dependency>
			<groupId>org.mule.modules</groupId>
			<artifactId>mule-apikit-module</artifactId>
			<version>1.11.5</version>
			<classifier>mule-plugin</classifier>
		</dependency>

		<dependency>
			<groupId>com.mulesoft.munit</groupId>
			<artifactId>munit-runner</artifactId>
			<version>3.4.0</version>
			<classifier>mule-plugin</classifier>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>com.mulesoft.munit</groupId>
			<artifactId>munit-tools</artifactId>
			<version>3.4.0</version>
			<classifier>mule-plugin</classifier>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.mule.module</groupId>
			<artifactId>mule-java-module</artifactId>
			<version>1.2.13</version>
			<classifier>mule-plugin</classifier>
		</dependency>
		<dependency>
			<groupId>com.mulesoft.connectors</groupId>
			<artifactId>mule-kafka-connector</artifactId>
			<version>4.10.2</version>
			<classifier>mule-plugin</classifier>
		</dependency>
	</dependencies>
	<repositories>
		<repository>
			<id>anypoint-exchange-v3</id>
			<name>Anypoint Exchange V3</name>
			<url>https://maven.anypoint.mulesoft.com/api/v3/maven</url>
			<layout>default</layout>
		</repository>
		<repository>
			<id>mulesoft-releases</id>
			<name>MuleSoft Releases Repository</name>
			<url>https://repository.mulesoft.org/releases/</url>
			<layout>default</layout>
		</repository>
	</repositories>
	<pluginRepositories>
		<pluginRepository>
			<id>mulesoft-releases</id>
			<name>mulesoft release repository</name>
			<layout>default</layout>
			<url>https://repository.mulesoft.org/releases/</url>
			<snapshots>
				<enabled>false</enabled>
			</snapshots>
		</pluginRepository>
	</pluginRepositories>

	<distributionManagement>
		<!-- Target Anypoint Organization Repository -->
		<repository>
			<id>Exchange</id>
			<name>Exchange Repository</name>
			<url>https://maven.anypoint.mulesoft.com/api/v1/organizations/${project.groupId}/maven</url>
			<layout>default</layout>
		</repository>
	</distributionManagement>

	<scm>
		<connection>scm:git:https://github.com/RoyalAholdDelhaize/becse-retail-prc-partners.git</connection>
		<developerConnection>scm:git:https://github.com/RoyalAholdDelhaize/becse-retail-prc-partners.git</developerConnection>
		<tag>prc-partners</tag>
	</scm>

</project>
